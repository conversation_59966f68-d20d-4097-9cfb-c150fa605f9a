# 凭证总览功能使用说明

## 功能概述

凭证总览功能是一个完整的会计凭证管理系统，支持实时查看、编辑和管理各种类型的会计凭证。该功能通过WebSocket与后台进行实时通信，提供流畅的用户体验。

## 功能特性

### 1. 凭证展示
- 以卡牌形式展示凭证信息
- 支持四种凭证类型：进项发票、销项发票、银行回单、工资单
- 每张卡牌包含原始数据信息和凭证信息两部分
- 支持审核状态标识

### 2. 筛选功能
页面顶部提供多种筛选选项：
- **按类型筛选**：进项发票、销项发票、银行回单、工资单
- **按审核状态筛选**：已审核、未审核
- 支持多选组合筛选

### 3. 凭证编辑
- 支持在线编辑凭证的所有字段
- 自动验证借贷平衡
- 支持添加/删除凭证明细
- 实时保存功能

### 4. 原始数据详情
- 点击"详情"按钮查看原始数据的完整信息
- 不同类型的原始数据有不同的展示格式
- 包含数据分析和统计信息

### 5. 实时通信
- 基于WebSocket的实时数据同步
- 支持多用户协作
- 自动推送新增凭证
- 实时更新凭证状态

## 页面结构

```
凭证总览页面
├── 筛选器区域
│   ├── 进项发票 □
│   ├── 销项发票 □
│   ├── 银行回单 □
│   ├── 工资单 □
│   ├── 已审核 □
│   └── 未审核 □
├── 凭证卡片区域
│   └── 凭证卡片
│       ├── 卡片头部（类型标签 + 审核状态）
│       ├── 原始数据信息
│       │   ├── 数据内容
│       │   └── 详情按钮
│       ├── 凭证信息
│       │   ├── 基本信息（类型、日期）
│       │   ├── 凭证明细表格
│       │   └── 执行者信息
│       └── 操作按钮（保存、删除）
└── 原始数据详情弹窗
```

## 数据格式

### 凭证数据结构
```typescript
interface Voucher {
  id: number;                    // 数据库唯一标识
  type: '记' | '借' | '转' | '结';  // 凭证类型
  record_date: string;           // 记账日期
  details: VoucherDetail[];      // 凭证明细
  executor: 'people' | 'history' | 'llm'; // 执行者
  reviewed: boolean;             // 是否已审核
  source_type: string;           // 原始数据类型
  source_info: SourceInfo;       // 原始数据信息
}
```

### 凭证明细结构
```typescript
interface VoucherDetail {
  summary: string;    // 摘要
  account: string;    // 科目
  debit: number;      // 借方金额
  credit: number;     // 贷方金额
}
```

### 原始数据信息
根据 `source_type` 的不同，`source_info` 包含不同的数据结构：

#### 发票信息（进项发票/销项发票）
```typescript
interface InvoiceInfo {
  fund_desc: string;  // 货物、劳务及服务名称
  amount: number;     // 金额
  tax: number;        // 税额
  total: number;      // 价税合计
  id: number;         // 发票ID
}
```

#### 银行回单信息
```typescript
interface BankReceiptInfo {
  total_income_amount: number;      // 收入总金额
  income_transaction_num: number;   // 收入总笔数
  total_expense_amount: number;     // 支出总金额
  expense_transaction_num: number;  // 支出总笔数
  months: string[];                 // 月份数组
}
```

#### 工资单信息
```typescript
interface PayrollInfo {
  total_gross_salary: number;           // 员工税前工资总和
  total_employer_contributions: number; // 公司缴纳社保公积金总和
  total_employee_deductions: number;    // 个人社保公积金总和
  months: string[];                     // 月份数组
}
```

## WebSocket 通信协议

### 客户端发送消息

1. **获取所有凭证**
```json
{ "type": "get_vouchers" }
```

2. **更新凭证**
```json
{
  "type": "update_voucher",
  "voucher": { /* 完整的凭证对象 */ }
}
```

3. **删除凭证**
```json
{
  "type": "delete_voucher",
  "id": 123
}
```

4. **获取原始数据详情**
```json
{
  "type": "get_source_detail",
  "voucher_id": 123
}
```

### 服务器推送消息

1. **凭证列表**
```json
{
  "type": "vouchers_list",
  "vouchers": [ /* 凭证数组 */ ]
}
```

2. **凭证更新**
```json
{
  "type": "voucher_updated",
  "voucher": { /* 更新后的凭证对象 */ }
}
```

3. **凭证删除**
```json
{
  "type": "voucher_deleted",
  "id": 123
}
```

4. **新增凭证**
```json
{
  "type": "voucher_added",
  "voucher": { /* 新凭证对象 */ }
}
```

## 使用流程

### 1. 启动系统

1. **启动Mock服务器**
```bash
cd apps/web-antd/src/mock
./start-mock.sh
```

2. **启动前端应用**
```bash
cd apps/web-antd
npm run dev
```

3. **访问凭证总览页面**
   - 在浏览器中打开应用
   - 导航到 "概览" > "凭证总览"

### 2. 查看凭证

- 页面加载后自动显示所有凭证
- 使用顶部筛选器按类型或审核状态筛选
- 每张卡牌显示凭证的关键信息

### 3. 编辑凭证

1. 在凭证卡片中直接修改字段
2. 添加或删除凭证明细
3. 点击"保存"按钮提交更改
4. 系统自动验证借贷平衡

### 4. 查看详情

1. 点击原始数据区域的"详情"按钮
2. 在弹窗中查看完整的原始数据信息
3. 查看数据分析和统计信息

### 5. 审核凭证

1. 勾选凭证卡片头部的"已审核"复选框
2. 系统自动保存审核状态
3. 已审核的凭证会有绿色边框标识

## 技术实现

### 前端技术栈
- **Vue 3** - 响应式框架
- **TypeScript** - 类型安全
- **Ant Design Vue** - UI组件库
- **WebSocket** - 实时通信
- **Vite** - 构建工具

### 组件结构
```
voucher-overview/
├── index.vue                    # 主页面组件
├── types.ts                     # 类型定义
├── composables/
│   └── useWebSocket.ts         # WebSocket逻辑
└── components/
    ├── VoucherCard.vue         # 凭证卡片组件
    └── SourceDataDetail.vue    # 原始数据详情组件
```

### 状态管理
- 使用 Vue 3 的 Composition API
- 响应式数据管理
- 自动筛选和更新

## 注意事项

1. **网络连接**
   - 确保WebSocket服务器正常运行
   - 检查防火墙设置
   - 注意端口占用问题

2. **数据验证**
   - 凭证明细必须借贷平衡
   - 必填字段不能为空
   - 数值字段格式验证

3. **性能优化**
   - 大量数据时考虑分页
   - 合理使用筛选功能
   - 避免频繁的WebSocket消息

4. **浏览器兼容性**
   - 现代浏览器支持WebSocket
   - 建议使用Chrome、Firefox、Safari等

## 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查服务器是否启动
   - 确认端口8080未被占用
   - 查看浏览器控制台错误信息

2. **数据不显示**
   - 检查网络连接
   - 确认服务器返回数据格式正确
   - 查看浏览器开发者工具

3. **保存失败**
   - 检查数据格式是否正确
   - 确认借贷是否平衡
   - 查看服务器日志

### 调试方法

1. **浏览器开发者工具**
   - Network标签查看WebSocket连接
   - Console标签查看错误信息
   - Vue DevTools查看组件状态

2. **服务器日志**
   - 查看WebSocket服务器控制台输出
   - 检查消息收发情况

## 扩展功能

### 可能的增强功能

1. **批量操作**
   - 批量审核
   - 批量删除
   - 批量导出

2. **高级筛选**
   - 日期范围筛选
   - 金额范围筛选
   - 关键词搜索

3. **数据分析**
   - 统计图表
   - 趋势分析
   - 报表生成

4. **权限管理**
   - 用户角色控制
   - 操作权限限制
   - 审计日志

这个凭证总览功能提供了一个完整的会计凭证管理解决方案，支持实时协作和高效的数据管理。
