import { defineOverridesPreferences } from '@vben/preferences';

import logoimg from '#/common/img/login.png';

/**
 * @description 项目配置文件
 * 只需要覆盖项目中的一部分配置，不需要的配置不用覆盖，会自动使用默认配置
 * !!! 更改配置后请清空缓存，否则可能不生效
 */
export const overridesPreferences = defineOverridesPreferences({
  // overrides
  app: {
    /**
     * 不要动这里  后端路由模式
     * frontend 前端路由
     * backend 后端路由
     */
    accessMode: 'backend',
    name: import.meta.env.VITE_APP_TITLE,
    /**
     * 不需要refresh token 由后端处理
     */
    enableRefreshToken: false,
    enableCheckUpdates: false,
    /**
     * 设置默认语言为中文
     */
    locale: 'zh-CN',
  },
  logo: {
    enable: true,
    source: logoimg,
  },
  footer: {
    /**
     * 不显示footer
     */
    enable: false,
  },
  tabbar: {
    /**
     * 标签tab 持久化 关闭
     */
    persist: false,
    // styleType: 'card',
  },
  theme: {
    /**
     * 浅色sidebar
     */
    semiDarkSidebar: false,
    /**
     * 圆角大小 换算比例为1.6px = 0.1radius
     * 这里为6px 与antd保持一致
     */
    radius: '0.375',
  },
  widget: {
    /**
     * 关闭语言切换功能
     */
    languageToggle: false,
    globalSearch: false,
  },
});
