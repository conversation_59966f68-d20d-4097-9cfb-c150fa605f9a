<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { <PERSON>ton, Card, Descriptions, Alert, Space, Tag, Input, Textarea } from 'ant-design-vue';
import { useWebSocket } from '@vueuse/core';

const wsUrl = ref('ws://**************:30065');
const testMessages = ref<Array<{ time: string; type: 'sent' | 'received' | 'error' | 'info'; content: string }>>([]);
const testStatus = ref<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
const customMessage = ref('{"type": "test", "message": "Hello WebSocket"}');

let testWebSocket: WebSocket | null = null;

const {
  close: wsClose,
  open: wsOpen,
  send: wsSend,
  status: wsStatus,
} = useWebSocket(wsUrl.value, {
  immediate: false,
  onConnected: () => {
    testStatus.value = 'connected';
    addMessage('info', 'WebSocket 连接成功');
  },
  onDisconnected: (_, event) => {
    testStatus.value = 'disconnected';
    addMessage('info', `WebSocket 连接断开: ${event.code} - ${event.reason}`);
  },
  onError: (_, event) => {
    testStatus.value = 'error';
    addMessage('error', `WebSocket 连接错误: ${JSON.stringify(event)}`);
  },
  onMessage: (_, event) => {
    const data = event.data;
    addMessage('received', `收到消息: ${data} (类型: ${typeof data})`);
    
    // 检查是否是有效的 JSON
    if (data && data !== 'undefined') {
      try {
        const parsed = JSON.parse(data);
        addMessage('info', `JSON 解析成功: ${JSON.stringify(parsed, null, 2)}`);
      } catch (error) {
        addMessage('error', `JSON 解析失败: ${error}`);
      }
    } else {
      addMessage('error', `收到无效数据: "${data}"`);
    }
  },
});

const addMessage = (type: 'sent' | 'received' | 'error' | 'info', content: string) => {
  testMessages.value.push({
    time: new Date().toLocaleTimeString(),
    type,
    content,
  });
  
  // 限制消息数量
  if (testMessages.value.length > 100) {
    testMessages.value = testMessages.value.slice(-50);
  }
};

const connectWebSocket = () => {
  if (testStatus.value === 'connected') {
    wsClose();
    return;
  }
  
  testStatus.value = 'connecting';
  addMessage('info', `尝试连接到: ${wsUrl.value}`);
  wsOpen();
};

const sendCustomMessage = () => {
  if (testStatus.value !== 'connected') {
    addMessage('error', '未连接到 WebSocket');
    return;
  }
  
  try {
    // 验证是否是有效的 JSON
    JSON.parse(customMessage.value);
    wsSend(customMessage.value);
    addMessage('sent', customMessage.value);
  } catch (error) {
    addMessage('error', `发送消息失败: ${error}`);
  }
};

const clearMessages = () => {
  testMessages.value = [];
};

const statusColor = computed(() => {
  switch (testStatus.value) {
    case 'connected': return 'success';
    case 'connecting': return 'processing';
    case 'error': return 'error';
    default: return 'default';
  }
});

const getMessageColor = (type: string) => {
  switch (type) {
    case 'sent': return '#1890ff';
    case 'received': return '#52c41a';
    case 'error': return '#ff4d4f';
    case 'info': return '#faad14';
    default: return '#666';
  }
};

// 监听状态变化
watch(wsStatus, (newStatus) => {
  console.log('WebSocket 状态变化:', newStatus);
  switch (newStatus) {
    case 'CONNECTING':
      testStatus.value = 'connecting';
      break;
    case 'OPEN':
      testStatus.value = 'connected';
      break;
    case 'CLOSED':
      testStatus.value = 'disconnected';
      break;
  }
});
</script>

<template>
  <div class="websocket-debug-panel">
    <Card title="WebSocket 连接调试工具" class="mb-4">
      <Space direction="vertical" style="width: 100%">
        <!-- 连接配置 -->
        <div>
          <label>WebSocket URL:</label>
          <Input 
            v-model:value="wsUrl" 
            placeholder="ws://localhost:8080"
            :disabled="testStatus === 'connected'"
            style="margin-top: 8px;"
          />
        </div>

        <!-- 连接状态 -->
        <div>
          <Tag :color="statusColor" style="margin-right: 8px;">
            状态: {{ testStatus }}
          </Tag>
          <Button 
            :type="testStatus === 'connected' ? 'danger' : 'primary'"
            @click="connectWebSocket"
            :loading="testStatus === 'connecting'"
          >
            {{ testStatus === 'connected' ? '断开连接' : '连接' }}
          </Button>
          <Button @click="clearMessages" style="margin-left: 8px;">
            清空消息
          </Button>
        </div>

        <!-- 发送自定义消息 -->
        <Card title="发送消息" size="small" v-if="testStatus === 'connected'">
          <Space direction="vertical" style="width: 100%;">
            <Textarea 
              v-model:value="customMessage"
              placeholder="输入 JSON 格式的消息"
              :rows="3"
            />
            <Button type="primary" @click="sendCustomMessage">
              发送消息
            </Button>
          </Space>
        </Card>

        <!-- 消息日志 -->
        <Card title="消息日志" size="small">
          <div 
            class="message-log"
            style="
              max-height: 400px; 
              overflow-y: auto; 
              background: #f5f5f5; 
              padding: 12px; 
              border-radius: 4px;
              font-family: monospace;
              font-size: 12px;
            "
          >
            <div 
              v-for="(msg, index) in testMessages" 
              :key="index"
              style="margin-bottom: 8px; padding: 4px 8px; border-radius: 4px; background: white;"
            >
              <span style="color: #666;">[{{ msg.time }}]</span>
              <span 
                :style="{ color: getMessageColor(msg.type), fontWeight: 'bold', marginLeft: '8px' }"
              >
                [{{ msg.type.toUpperCase() }}]
              </span>
              <div style="margin-top: 4px; word-break: break-all;">
                {{ msg.content }}
              </div>
            </div>
            
            <div v-if="testMessages.length === 0" style="text-align: center; color: #999;">
              暂无消息
            </div>
          </div>
        </Card>

        <!-- 常见问题解决方案 -->
        <Card title="常见问题解决方案" size="small">
          <div style="font-size: 14px; line-height: 1.6;">
            <h4>如果遇到 "undefined" JSON 解析错误：</h4>
            <ol>
              <li><strong>检查服务器端</strong>：确保服务器发送的是有效的 JSON 字符串</li>
              <li><strong>检查心跳消息</strong>：某些 WebSocket 库会发送空的心跳消息</li>
              <li><strong>检查连接状态</strong>：确保在连接建立后才发送消息</li>
              <li><strong>添加消息验证</strong>：在客户端添加消息有效性检查</li>
            </ol>

            <h4>调试步骤：</h4>
            <ol>
              <li>连接到 WebSocket 服务器</li>
              <li>观察接收到的消息类型和内容</li>
              <li>检查是否有 "undefined" 或空消息</li>
              <li>发送测试消息验证双向通信</li>
            </ol>

            <Alert
              message="提示"
              description="如果频繁收到 'undefined' 消息，可能是服务器端的心跳机制或错误处理有问题"
              type="info"
              show-icon
              style="margin-top: 16px;"
            />
          </div>
        </Card>
      </Space>
    </Card>
  </div>
</template>

<style scoped>
.websocket-debug-panel {
  max-width: 800px;
  margin: 0 auto;
}

.message-log {
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
