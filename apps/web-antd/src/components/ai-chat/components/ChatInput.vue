<script setup lang="ts">
  import type { UploadFile } from 'ant-design-vue/es/upload/interface';
  import type { PromptsProps, SuggestionProps } from 'ant-design-x-vue';

  import type { UploadedFile } from '../types/chat';

  import { computed, h, ref } from 'vue';

  import { LinkOutlined, SmileOutlined } from '@ant-design/icons-vue';
  import { Button } from 'ant-design-vue';
  import { Prompts, Sender, Suggestion } from 'ant-design-x-vue';

  import FileUpload from './FileUpload.vue';

  interface Props {
    fileItems: UploadFile[];
    fileUploadOpen: boolean;
    inputValue: string;
    loading: boolean;
    uploadedFiles: UploadedFile[];
  }

  interface Emits {
    (e: 'update:inputValue', value: string): void;
    (e: 'update:fileUploadOpen', value: boolean): void;
    (e: 'submit', content: string): void;
    (e: 'file-change', data: { fileList: UploadFile[] }): void;
    (e: 'file-upload', firstFile: File, fileList: FileList): void;
    (e: 'prompt-select', info: { data: PromptsProps['items'][number] }): void;
    (e: 'suggestion-select', itemVal: string): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const inputRef = ref();
  const fileUploadRef = ref();

  // 计算是否可以发送消息（有文本内容或有附件时可以发送）
  const canSendMessage = computed(() => {
    const hasContent = props.inputValue.trim().length > 0;
    const hasFiles = props.uploadedFiles.length > 0;
    return hasContent || hasFiles;
  });

  // 自定义发送按钮
  const customActions = (oriNode: any, { components }: any) => {
    const { SendButton } = components;

    return h(
      SendButton,
      {
        disabled: !canSendMessage.value,
        loading: props.loading,
        onClick: () => {
          if (canSendMessage.value) {
            handleSubmit(props.inputValue);
          }
        },
      },
      '发送',
    );
  };

  type SuggestionItems = Exclude<SuggestionProps['items'], () => void>;

  const suggestions: SuggestionItems = [
    { label: '生成凭证', value: '生成凭证' },
  ];

  const promptTemplates: PromptsProps['items'] = [
    {
      icon: h(SmileOutlined, { style: { color: '#52C41A' } }),
      key: '1',
      label: '生成凭证',
    },
  ];

  const handleSubmit = (content: string) => {
    emit('submit', content);
  };

  const handleInputChange = (value: string) => {
    emit('update:inputValue', value);
  };

  const handleFileUploadOpenChange = (open: boolean) => {
    emit('update:fileUploadOpen', open);
  };

  const handleFileChange = (data: { fileList: UploadFile[] }) => {
    emit('file-change', data);
  };

  const handleFileUpload = (firstFile: File, fileList: FileList) => {
    emit('file-upload', firstFile, fileList);
  };

  const handlePromptSelect = (info: {
    data: PromptsProps['items'][number];
  }) => {
    emit('prompt-select', info);
  };

  const handleSuggestionSelect = (itemVal: string) => {
    emit('suggestion-select', itemVal);
  };

  const handleSenderChange = (
    nextVal: string,
    { onTrigger }: { onTrigger?: (show?: boolean) => void } = {},
  ) => {
    if (nextVal === '/') {
      onTrigger?.();
    } else if (!nextVal) {
      onTrigger?.(false);
    }
    handleInputChange(nextVal);
  };

  const toggleFileUpload = () => {
    handleFileUploadOpenChange(!props.fileUploadOpen);
  };

  defineExpose({
    fileUploadRef,
    inputRef,
  });
</script>

<template>
  <div class="chat-input">
    <div class="prompts-container">
      <Prompts
        :items="promptTemplates"
        :on-item-click="handlePromptSelect"
        style="transform: scale(0.8); transform-origin: left center"
      />
    </div>

    <div class="sender-container">
      <Suggestion :items="suggestions" @select="handleSuggestionSelect">
        <template #default="{ onTrigger }: any">
          <Sender
            :value="inputValue"
            ref="inputRef"
            placeholder="输入消息，按 Enter 发送..."
            :loading="loading"
            :actions="customActions"
            @submit="handleSubmit"
            @paste-file="handleFileUpload"
            @change="(val) => handleSenderChange(val, { onTrigger })"
          >
            <template #prefix>
              <Button
                type="text"
                :icon="h(LinkOutlined)"
                @click="toggleFileUpload"
              />
            </template>

            <template #header>
              <FileUpload
                ref="fileUploadRef"
                :is-open="fileUploadOpen"
                :items="fileItems"
                :loading="loading"
                @update:is-open="handleFileUploadOpenChange"
                @file-change="handleFileChange"
                @file-upload="handleFileUpload"
              />
            </template>
          </Sender>
        </template>
      </Suggestion>
    </div>
  </div>
</template>

<style scoped lang="scss">
  .chat-input {
    .prompts-container {
      padding-bottom: 6px;

      :deep(.ant-prompts) {
        .ant-prompts-item {
          min-height: 28px !important;
          padding: 4px 10px !important;
          margin: 2px !important;
          font-size: 11px !important;
          background: linear-gradient(
            135deg,
            #f8fafc 0%,
            #e2e8f0 100%
          ) !important;
          border: 1px solid rgb(255 255 255 / 60%) !important;
          border-radius: 6px !important;
          transition: all 0.2s ease !important;

          &:hover {
            color: white !important;
            background: linear-gradient(
              135deg,
              #667eea 0%,
              #764ba2 100%
            ) !important;
            box-shadow: 0 4px 12px rgb(102 126 234 / 30%) !important;
            transform: translateY(-1px) !important;
          }
        }

        .ant-prompts-item-icon {
          font-size: 12px !important;
        }

        .ant-prompts-item-label {
          font-size: 11px !important;
          font-weight: 500 !important;
        }
      }
    }
  }
</style>
