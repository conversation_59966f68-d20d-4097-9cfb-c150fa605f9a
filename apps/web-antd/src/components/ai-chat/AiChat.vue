<script setup lang="ts">
  import type { UploadFile } from 'ant-design-vue/es/upload/interface';

  import type { ChatMessage } from '#/store/modules/ai-chat';

  import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

  import dayjs from 'dayjs';

  import { safeWebSocketMessageHandler } from '#/utils/websocket-message-filter';

  // 导入子组件
  import ChatHeader from './components/ChatHeader.vue';
  import ChatInput from './components/ChatInput.vue';
  import ChatMessages from './components/ChatMessages.vue';
  import { useCompanySelection } from './composables/useCompanySelection';
  import { useFileUpload } from './composables/useFileUpload';
  import { useMessageHandling } from './composables/useMessageHandling';
  // 导入composables
  import { useWebSocketConnection } from './composables/useWebSocketConnection';
  import MessageDetailModal from './MessageDetailModal.vue';

  import 'dayjs/locale/zh-cn';

  // 设置dayjs的默认locale
  try {
    dayjs.locale('zh-cn');
  } catch (error) {
    console.warn('Failed to set dayjs locale to zh-cn:', error);
  }

  const WS_URL = 'ws://192.168.20.211:30065';

  // 使用composables
  const {
    connectionStatus,
    reconnectWebSocket,
    sendMessageWithFiles,
    sendUpdateClientInfoMessage,
    setOnConnectedCallback,
    taskProgressMap,
    wsClose,
    wsOpen,
  } = useWebSocketConnection(WS_URL, (event: MessageEvent) => {
    // 使用安全的消息处理器
    safeWebSocketMessageHandler(
      event.data,
      (data: any) => {
        console.log('处理有效的 WebSocket 消息:', data);
        handleWebSocketMessage(data, scrollToBottom);
      },
      {
        logHeartbeat: false,
        logInvalidMessages: true,
        onError: (error: string, rawData: any) => {
          console.error(`WebSocket 消息处理错误: ${error}`, rawData);
        },
      },
    );
  });

  const {
    clearFiles,
    fileChange,
    getFilesForMessage,
    handleFileUpload,
    isOpen: fileUploadOpen,
    uploadedFiles,
    items: fileItems,
  } = useFileUpload();

  const {
    companyList,
    fetchCompanyNames,
    handleCompanyChange: handleCompanyChangeBase,
    handleMonthSelectChange: handleMonthSelectChangeBase,
    initializationError,
    isInitializing,
    monthOptions,
    selectedCompany,
    selectedMonth,
  } = useCompanySelection();

  const {
    handleSubmit: handleMessageSubmit,
    handleWebSocketMessage,
    loading,
    messages,
  } = useMessageHandling(taskProgressMap);

  // 本地状态
  const inputValue = ref('');
  const detailModalVisible = ref(false);
  const selectedMessage = ref<ChatMessage | null>(null);

  // 滚动到底部的函数
  const scrollToBottom = () => {
    console.log('scrollToBottom 被调用');

    // 使用 nextTick 确保 DOM 已更新
    nextTick(() => {
      // 按优先级尝试多个可能的滚动容器
      const containers = [
        '.messages-container', // 主要滚动容器
        '.chat-messages', // ChatMessages 组件的根容器
        '.bubble-list', // Bubble.List 组件
        '.ant-bubble-list', // ant-design-x 的 bubble list
      ];

      for (const selector of containers) {
        const container = document.querySelector(selector);
        if (container) {
          console.log(`检查容器: ${selector}`, {
            clientHeight: container.clientHeight,
            hasOverflow: container.scrollHeight > container.clientHeight,
            scrollHeight: container.scrollHeight,
          });

          // 检查是否需要滚动（内容高度大于容器高度）
          if (container.scrollHeight > container.clientHeight) {
            console.log(`找到可滚动容器: ${selector}`);

            // 立即滚动到底部（不使用平滑滚动，确保立即生效）
            container.scrollTop = container.scrollHeight;

            console.log(`滚动完成，scrollTop: ${container.scrollTop}`);
            return;
          }
        }
      }

      console.warn('未找到可滚动的容器，尝试强制滚动主容器');
      // 如果没有找到可滚动容器，尝试强制滚动主容器
      const mainContainer = document.querySelector('.messages-container');
      if (mainContainer) {
        mainContainer.scrollTop = mainContainer.scrollHeight;
        console.log('强制滚动主容器完成');
      }
    });
  };

  // 处理公司选择变化
  const handleCompanyChange = (value: string) => {
    handleCompanyChangeBase(value, () => {
      sendUpdateClientInfoMessage(selectedCompany.value, selectedMonth.value);
    });
  };

  // 处理月份选择变化
  const handleMonthChange = (value: string) => {
    handleMonthSelectChangeBase(value, () => {
      sendUpdateClientInfoMessage(selectedCompany.value, selectedMonth.value);
    });
  };

  // 处理消息提交
  const handleSubmit = async (content: string) => {
    await handleMessageSubmit(
      content,
      uploadedFiles.value,
      (messageContent: string) => {
        const files = getFilesForMessage();
        sendMessageWithFiles(
          messageContent,
          files,
          selectedCompany.value,
          selectedMonth.value,
        );
        // 清空输入框和文件
        inputValue.value = '';
        clearFiles();
      },
      scrollToBottom,
    );
  };

  // 处理文件变化
  const handleFileChange = (data: { fileList: UploadFile[] }) => {
    fileChange(data, selectedCompany.value);
  };

  // 处理文件上传
  const handleFileUploadWrapper = (firstFile: File, fileList: FileList) => {
    // 这里需要获取attachmentsRef，暂时传null
    handleFileUpload(firstFile, fileList, null);
  };

  // 处理Prompt选择
  const handlePromptSelect = (info: { data: any }) => {
    const label = info.data.label;
    if (typeof label === 'string') {
      handleSubmit(label);
    }
  };

  // 处理建议选择
  const handleSuggestionSelect = (itemVal: string) => {
    inputValue.value = `[${itemVal}]:`;
  };

  // 显示消息详情
  const showMessageDetail = (message: ChatMessage) => {
    selectedMessage.value = message;
    detailModalVisible.value = true;
  };

  // 重新加载页面
  const reloadPage = () => {
    window.location.reload();
  };

  // 设置WebSocket消息处理
  const setupWebSocketMessageHandler = () => {
    // 这里需要设置WebSocket的onMessage处理器
    // 由于useWebSocketConnection可能需要修改来支持外部消息处理器
    // 暂时保留这个结构
  };

  onMounted(async () => {
    console.log('AI聊天组件开始初始化...');

    try {
      // 获取公司列表
      await fetchCompanyNames();
      console.log('公司列表获取完成:', companyList.value);

      // 等待一小段时间确保状态更新
      await nextTick();

      // 检查初始化是否成功
      if (initializationError.value) {
        console.error('初始化失败:', initializationError.value);
        return;
      }
    } catch (error) {
      console.error('获取公司列表失败:', error);
      return;
    }

    try {
      // 设置WebSocket连接成功后的回调
      setOnConnectedCallback(() => {
        console.log('WebSocket连接成功，发送更新公司信息消息');
        sendUpdateClientInfoMessage(selectedCompany.value, selectedMonth.value);
      });

      // 打开WebSocket连接
      wsOpen();
      console.log('WebSocket连接已启动');

      // 设置消息处理器
      setupWebSocketMessageHandler();
    } catch (error) {
      console.error('WebSocket连接失败:', error);
    }

    // 监听消息列表变化，自动滚动到底部
    watch(
      messages,
      () => {
        // scrollToBottom 内部已经有 nextTick，这里直接调用
        scrollToBottom();
      },
      { deep: true },
    );

    // 初始化完成
    console.log('AI聊天组件初始化完成');
  });

  onUnmounted(() => {
    wsClose();
  });
</script>

<template>
  <div class="chat-container">
    <div class="chat-card">
      <!-- 聊天头部 -->
      <ChatHeader
        :company-list="companyList"
        :selected-company="selectedCompany"
        :selected-month="selectedMonth"
        :month-options="monthOptions"
        :connection-status="connectionStatus"
        @company-change="handleCompanyChange"
        @month-change="handleMonthChange"
        @reconnect="reconnectWebSocket"
      />

      <div class="chat-layout">
        <div class="chat-main">
          <!-- 消息容器 -->
          <div class="messages-container">
            <ChatMessages
              :messages="messages"
              :loading="loading"
              :is-initializing="isInitializing"
              :initialization-error="initializationError"
              @show-message-detail="showMessageDetail"
              @reload-page="reloadPage"
            />
          </div>

          <!-- 输入容器 -->
          <div class="input-container">
            <ChatInput
              v-model:input-value="inputValue"
              v-model:file-upload-open="fileUploadOpen"
              :loading="loading"
              :uploaded-files="uploadedFiles"
              :file-items="fileItems"
              @submit="handleSubmit"
              @file-change="handleFileChange"
              @file-upload="handleFileUploadWrapper"
              @prompt-select="handlePromptSelect"
              @suggestion-select="handleSuggestionSelect"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 详情模态框 -->
    <MessageDetailModal
      v-model:visible="detailModalVisible"
      :message="selectedMessage"
    />
  </div>
</template>

<style scoped lang="scss">
  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }

    50% {
      opacity: 0.7;
      transform: scale(1.2);
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .chat-container {
    height: 100%;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }

  .chat-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgb(0 0 0 / 10%);

    :deep(.ant-card-body) {
      height: 100%;
      padding: 0;
    }
  }

  .chat-layout {
    display: flex;
    flex: 1;
    height: calc(100% - 68px);
    overflow: hidden;
  }

  .chat-main {
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .messages-container {
    flex: 1;
    padding: 8px;
    overflow-y: auto;
    background: rgb(255 255 255 / 80%);
    backdrop-filter: blur(10px);

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #5a67d8, #6b46c1);
      }
    }

    &::-webkit-scrollbar-track {
      background: rgb(255 255 255 / 10%);
      border-radius: 6px;
    }
  }

  .input-container {
    padding: 8px 16px;
    background: rgb(255 255 255 / 95%);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgb(255 255 255 / 20%);
    box-shadow: 0 -4px 16px rgb(0 0 0 / 5%);
  }
</style>
