<script setup lang="ts">
interface Props {
    /**
     * 给ul添加的class name
     */
    classname?: string;
    value?: string;
    type?: string;
}
withDefaults(defineProps<Props>(), {
    classname: '',
    value: '',
    type: '',
});
const units: string[] = ['亿', '千', '百', '十', '万', '千', '百', '十', '元', '角', '分'];
const unitslength: number = units.length;
</script>
<template>
    <ul :class="[classname]">
        <li
            v-for="(itm, i) in units"
            :key="itm"
        >
            {{ type === 'head' ? itm : value[unitslength - 1 - i] }}
        </li>
    </ul>
</template>
<style lang="scss" scoped>
ul {
    pointer-events: none;
}
</style>
