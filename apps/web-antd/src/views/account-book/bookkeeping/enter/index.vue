<script setup lang="ts">
import type { Dayjs } from 'dayjs';

import type { ListItm } from '../../index.d.ts';

import type { VoucherSaveData, VoucherSaveDataItm, VoucherTemplate } from '#/api/account-book/bookkeeping/index';

import { onMounted, reactive, ref, watch } from 'vue';

import { SvgAttention, SvgShortcutKeys } from '@vben/icons';
import { useUserStore } from '@vben/stores';

import Icon, { RightOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { addVoucherTemplate, getVoucherNo, setVoucherSave } from '#/api/account-book/bookkeeping/index';
import { commondata } from '#/common/index';
import { useGlobalLoading } from '#/hooks/useGlobalLoading';
import { uTcurrencyToUpperCase, uTgetTime, uTpadNumber } from '#/utils/index';
import OuterBoundary from '#/views/components/outer-boundary.vue';

import quickInput from '../components/quick-input/index.vue';
import Attachment from './attachment/index.vue';
import VoucherEditing from './VoucherEditing.vue';

const useLoading = useGlobalLoading();

export type StateMol = {
    attachmentShow: boolean; // 附件是否显示
    creator: string; // 制单人
    nubval: string;
};
const temNameShow = ref<boolean>(false); // 模板名字编写弹窗
const temNameFormRef = ref<any>(null); // 模板名字输入的弹窗FORM表单
const temNameId = ref<string>('');
const temNameForm = reactive<{
    name: string; // 模板名称
    saveAmount: boolean; // 是否保存金额
}>({
    name: '',
    saveAmount: false,
});
const dateFormat = 'YYYY-MM';
const currdate = uTgetTime();
const dateValue = ref<Dayjs>(dayjs(`${currdate[0]}-${currdate[1]}`, dateFormat));
const state = reactive<StateMol>({
    creator: '', // 制单人
    nubval: '', // 编号
    attachmentShow: false,
});
// 编辑表格的ref
const editTableRef = ref<any>();
const book_type_val = ref<string>('记');
// 快速编辑是否显示
const quickEditShow = ref<boolean>(false);
type BookTypeItm = {
    value: string;
};
const changeBookType = async (value: string, data: BookTypeItm) => {
    // 判断日期
    if (!dateValue.value) {
        message.error('请选择日期');
        return;
    }
    const res = await getVoucherNo(dateValue.value?.year(), dateValue.value?.month() + 1, data.value);
    if (res.returnCode == '200') {
        state.nubval = uTpadNumber(Number(res.data), 3);
    }
};
onMounted(() => {
    const userStore = useUserStore();
    state.creator = userStore.userInfo?.realName as string;
    changeBookType('记', { value: '记' });
});
const save = async (addtype?: boolean) => {
    const tips = editTableRef.value.data_validation();
    if (tips) {
        message.warning(tips, 10);
        return;
    }
    useLoading.setShow(true);
    const detail: VoucherSaveDataItm[] = [];
    const tablestate = editTableRef.value.state;
    tablestate.list.map((v: any) => {
        const json: any = {};
        if (v.borrower) {
            json.debit = v.borrower;
        }
        if (v.lender) {
            json.credit = v.lender;
        }
        json.subjectId = v.subject?.id;
        json.summary = v.abstract?.text;

        if (json.subjectId && json.summary) {
            detail.push(json);
        }
    });
    const todata: VoucherSaveData = {
        attachmentCount: 0, // 附件数
        credit: tablestate.lenderAll, // 贷方总金额
        dateTime: dateValue.value.valueOf(), // 时间戳
        debit: tablestate.borrowerAll, // 借方总金额
        detail,
        insertMode: false, // TODO 不知道什么意思
        insertNumber: null, // TODO 不知道什么意思
        insertWord: null, // TODO 不知道什么意思
        voucherNo: Number(state.nubval), // 凭证编号
        voucherNumber: Number(state.nubval), // 凭证编号和上面的值一样
        voucherType: 'NORMAL', // TODO 不知道什么意思
        voucherWord: book_type_val.value, // 凭证类型文字
    };
    setVoucherSave(todata)
        .then((res: any) => {
            console.log(99, res);
            useLoading.setShow(false);
            if (res.returnCode === '200') {
                // 成功
                message.success('保存成功');
                if (addtype) {
                    // 新增的话保存后直接清空数据
                    editTableRef.value.clearTableData();
                }
            } else {
                message.error(res.returnMsg);
            }
        })
        .catch(() => {
            useLoading.setShow(false);
        });
};
const saveTemplate = (type?: string) => {
    temNameFormRef.value.validate().then((namedata: any) => {
        useLoading.setShow(true);
        const detail: VoucherSaveDataItm[] = [];
        editTableRef.value.state.list.map((v: any) => {
            const json: any = {};
            if (v.borrower) {
                json.debit = v.borrower;
            }
            if (v.lender) {
                json.credit = v.lender;
            }
            json.subjectId = v.subject?.id;
            json.summary = v.abstract?.text;

            if (json.subjectId && json.summary) {
                detail.push(json);
            }
        });
        const todata: VoucherTemplate = {
            attachmentCount: 0, // 附件数
            detail,
            isAmount: namedata.saveAmount,
            name: namedata.name,
        };
        if (type === 'modify') {
            todata.id = temNameId.value;
        }
        addVoucherTemplate(todata)
            .then((res: any) => {
                console.log(99, res);
                useLoading.setShow(false);
                if (res.returnCode === '200') {
                    // 成功
                    message.success('保存成功');
                    temNameShow.value = false;
                    temNameId.value = res.data.id;
                } else {
                    message.warning(res.returnMsg);
                }
            })
            .catch(() => {
                useLoading.setShow(false);
            });
    });
};
const saveTemplateNameShow = () => {
    // const tips = data_validation('template');
    const tips = editTableRef.value.data_validation('template');
    if (tips) {
        message.warning(tips, 10);
        return;
    }
    // 最少要填两行数据
    let fillInNum = 0;
    editTableRef.value.state.list.map((v: any) => {
        if (v.subject && v.abstract) {
            fillInNum++;
        }
    });
    if (fillInNum < 2) {
        message.warning('最少要编辑2行');
        return;
    }
    temNameShow.value = true;
};
</script>
<template>
    <OuterBoundary>
        <div class="mainbox cont flex flex-1 flex-col rounded-md bg-white">
            <div
                class="quick-btn"
                @click="
                    () => {
                        quickEditShow = !quickEditShow;
                    }
                "
            >
                快速凭证<RightOutlined />
            </div>
            <div class="flex flex-1 flex-col">
                <div class="p-2">
                    <a-flex justify="space-between">
                        <a-space>
                            <a-select
                                size="small"
                                style="width: 100px"
                                :options="commondata.bookTypes"
                                v-model:value="book_type_val"
                                :field-names="{ label: 'value', value: 'value' }"
                                @change="changeBookType"
                            />
                            <a-input
                                placeholder="Basic usage"
                                style="width: 70px"
                                size="small"
                                v-model:value="state.nubval"
                                disabled
                            >
                                <template #suffix>号</template>
                            </a-input>
                            凭证日期:
                            <a-date-picker
                                size="small"
                                picker="month"
                                v-model:value="dateValue"
                            />
                            附件数量:
                            <a-input
                                placeholder="Basic usage"
                                style="width: 50px"
                                size="small"
                                disabled
                                default-value="0022"
                            />
                            <a-button size="small">附件</a-button>
                        </a-space>
                        <a-space>
                            <a-button
                                type="primary"
                                size="small"
                                @click="
                                    () => {
                                        save();
                                    }
                                "
                            >
                                保存
                            </a-button>
                            <a-button
                                type="primary"
                                size="small"
                                @click="
                                    () => {
                                        save(true);
                                    }
                                "
                            >
                                保存并新增
                            </a-button>
                            <a-button size="small">使用模板</a-button>
                            <a-button
                                size="small"
                                @click="saveTemplateNameShow"
                            >
                                保存模板
                            </a-button>
                        </a-space>
                    </a-flex>
                </div>
                <a-flex class="m-2 flex-1">
                    <a-flex flex="1">
                        <VoucherEditing ref="editTableRef" />
                    </a-flex>
                    <Attachment v-if="state.attachmentShow" />
                </a-flex>
            </div>
            <a-divider style="margin: 4px 0" />
            <div class="bottom-box flex justify-between p-3">
                <div>制单人： {{ state.creator }}</div>
                <div>
                    <span class="d1">
                        <a-popover title="Title">
                            <template #content>
                                <p>Content</p>
                            </template>
                            <Icon>
                                <SvgShortcutKeys />
                            </Icon>
                            快捷键
                        </a-popover>
                    </span>
                    <span class="d1">
                        <a-popover title="Title">
                            <template #content>
                                <p>Content</p>
                            </template>
                            <Icon>
                                <SvgAttention />
                            </Icon>
                            注意
                        </a-popover>
                    </span>
                </div>
            </div>
        </div>
        <a-modal
            v-model:open="temNameShow"
            title="请输入模板名称"
            :footer="false"
        >
            <div class="cont">
                <a-form
                    :model="temNameForm"
                    ref="temNameFormRef"
                >
                    <a-form-item
                        label="模板名称"
                        name="name"
                        :rules="[{ required: true, message: '请输入模板名称' }]"
                    >
                        <a-input
                            maxlength="50"
                            v-model:value="temNameForm.name"
                        />
                    </a-form-item>
                    <a-form-item>
                        <a-checkbox v-model:checked="temNameForm.saveAmount">是否保存金额</a-checkbox>
                    </a-form-item>
                </a-form>
                <div class="btnbox">
                    <a-button
                        type="primary"
                        @click="
                            () => {
                                saveTemplate();
                            }
                        "
                    >
                        保存新模板
                    </a-button>
                    &nbsp;&nbsp;
                    <a-button
                        type="primary"
                        v-if="temNameId"
                        @click="
                            () => {
                                saveTemplate('modify');
                            }
                        "
                    >
                        更新现有模板
                    </a-button>
                </div>
            </div>
        </a-modal>
        <quickInput
            v-if="quickEditShow"
            @close="quickEditShow = false"
        />
    </OuterBoundary>
</template>
<style lang="scss" scoped>
.mainbox {
    position: relative;

    .quick-btn {
        position: absolute;
        top: 34px;
        left: 0;
        z-index: 2;
        width: 24px;
        height: 90px;
        font-size: 12px;
        line-height: 24px;
        color: #fff;
        text-align: center;
        letter-spacing: 3px;
        cursor: pointer;
        background-color: rgb(249 128 7);
        border-radius: 0 5px 5px 0;
        writing-mode: vertical-lr;
    }
}

.bottom-box {
    .d1 {
        display: inline-block;
        margin: 0 10px;
        font-size: 14px;

        .ico {
            display: inline-block;
            vertical-align: middle;
        }
    }
}

/* .tablebox {
    width: 95%;
    margin: 0 auto;
    border: solid 1px rgb(217 218 220);
    border-bottom: none;
} */
</style>
