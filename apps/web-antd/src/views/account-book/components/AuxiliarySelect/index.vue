<template>
  <a-select
    v-model:value="selectedValue"
    :placeholder="placeholder"
    :options="options"
    :field-names="fieldNames"
    show-search
    allow-clear
    :filter-option="filterOption"
    @change="handleChange"
  />
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

interface AuxiliaryOption {
  id: string;
  name: string;
  code?: string;
}

interface Props {
  modelValue?: string;
  placeholder?: string;
  options?: AuxiliaryOption[];
  type?: 'supplier' | 'customer' | 'employee' | 'department';
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'change', value: string, option: AuxiliaryOption): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择辅助核算',
  options: () => [],
  type: 'supplier',
});

const emit = defineEmits<Emits>();

const selectedValue = ref(props.modelValue);

const fieldNames = {
  label: 'name',
  value: 'id',
};

const options = computed(() => {
  return props.options.map(item => ({
    ...item,
    label: item.name,
    value: item.id,
  }));
});

const filterOption = (input: string, option: any) => {
  return option.name.toLowerCase().includes(input.toLowerCase()) ||
         (option.code && option.code.toLowerCase().includes(input.toLowerCase()));
};

const handleChange = (value: string) => {
  selectedValue.value = value;
  emit('update:modelValue', value);
  
  const selectedOption = props.options.find(item => item.id === value);
  if (selectedOption) {
    emit('change', value, selectedOption);
  }
};

watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue;
});
</script>
