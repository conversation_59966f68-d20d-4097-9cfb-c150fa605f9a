# VxeGrid到Ant Design表格迁移总结

## 🎯 迁移原因

由于VxeGrid表格组件渲染存在问题，决定将表格组件替换为Ant Design的原生表格组件，以确保数据能够正确显示。

## 🔄 主要变更

### 1. 移除VxeGrid相关代码

**移除的导入：**
```typescript
// ❌ 移除
import { useVbenVxeGrid } from '#/adapter/vxe-table';

// ❌ 移除
const [BasicTable, gridApi] = useVbenVxeGrid({...});
```

**移除的配置：**
- VxeGrid的gridOptions配置
- gridApi.setGridOptions()调用
- VxeGrid相关的列配置（bankColumns, invoiceColumns）

### 2. 添加Ant Design表格配置

**新增导入：**
```typescript
import { computed, h, onMounted, ref, watch } from 'vue';
```

**新增列配置：**
```typescript
const antdColumns = computed(() => {
  if (activeTab.value === 'bank') {
    return [
      // 银行回单列配置
      {
        title: '公司名称',
        dataIndex: 'company_name',
        key: 'company_name',
        width: 180,
        ellipsis: true,
      },
      // ... 其他列
    ];
  } else {
    return [
      // 发票列配置
      {
        title: '发票号码',
        dataIndex: 'digital_invoice_number',
        key: 'digital_invoice_number',
        width: 180,
        ellipsis: true,
      },
      // ... 其他列
    ];
  }
});
```

### 3. 更新模板

**替换前：**
```vue
<BasicTable :key="tableKey">
  <template #action="{ row }">
    <a :href="buildFileUrl(row)" target="_blank">查看原文件</a>
  </template>
</BasicTable>
```

**替换后：**
```vue
<a-table
  :key="tableKey"
  :columns="antdColumns"
  :data-source="tableData"
  :loading="loading"
  :pagination="{
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
  }"
  :scroll="{ x: 1200 }"
  row-key="_id"
  size="middle"
/>
```

## 📊 列配置对比

### 银行回单列配置
| 列名 | 字段名 | 宽度 | 特性 |
|------|--------|------|------|
| 公司名称 | company_name | 180 | 省略号 |
| 交易时间 | transaction_time | 150 | - |
| 月份 | month | 80 | - |
| 类型 | type | 80 | - |
| 凭证编号 | voucher_number | 120 | 省略号 |
| 交易流水号 | transaction_id | 150 | 省略号 |
| 账户名称 | account_name | 180 | 省略号 |
| 银行名称 | bank_name | 120 | - |
| 对方账户名称 | conterpary_account_name | 150 | 省略号 |
| 金额 | amount | 120 | 右对齐 |
| 币种 | currency | 60 | - |
| 摘要 | summary | 150 | 省略号 |
| 备注 | note | 120 | 省略号 |
| 操作 | - | 120 | 固定右侧 |

### 发票列配置
| 列名 | 字段名 | 宽度 | 特性 |
|------|--------|------|------|
| 发票号码 | digital_invoice_number | 180 | 省略号 |
| 凭证号 | voucher_number | 120 | 省略号 |
| 发票状态 | status | 100 | - |
| 发票类型 | type | 120 | - |
| 开票日期 | issue_date | 120 | - |
| 货物或劳务明细 | goods_name | 200 | 省略号 |
| 销方名称 | seller_name | 180 | 省略号 |
| 购方名称 | buyer_name | 180 | 省略号 |
| 操作 | - | 120 | 固定右侧 |

## 🔧 技术细节

### 操作列的自定义渲染
使用Vue的h函数创建操作列的链接：
```typescript
customRender: ({ record }: any) => {
  return h('a', {
    href: buildFileUrl(record),
    target: '_blank',
    class: 'table-action-link',
  }, '查看原文件');
},
```

### 类型安全
为了确保TypeScript类型安全，使用了类型断言：
```typescript
align: 'right' as const,
fixed: 'right' as const,
```

### 分页配置
提供了完整的分页功能：
```typescript
:pagination="{
  showSizeChanger: true,      // 显示页面大小选择器
  showQuickJumper: true,      // 显示快速跳转
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
}"
```

## 🎨 样式保持

保持了原有的样式配置：
- 表格字体大小：14px
- 表头背景色：#f7f8fa
- 表头字体粗细：600
- 分页右对齐
- 操作链接样式

## ✅ 优势

1. **简单直接**：Ant Design表格配置更直观
2. **类型安全**：更好的TypeScript支持
3. **功能完整**：内置分页、排序、筛选等功能
4. **响应式**：自动响应数据变化
5. **性能稳定**：避免了VxeGrid的渲染问题

## 🧪 验证要点

迁移后需要验证：

1. **数据显示**：表格能正确显示API返回的数据
2. **列配置**：所有列都正确显示，宽度合适
3. **操作列**：查看原文件链接正常工作
4. **分页功能**：分页、页面大小选择、快速跳转正常
5. **响应式**：Tab切换、公司选择、搜索功能正常
6. **加载状态**：loading状态正确显示
7. **横向滚动**：表格宽度超出时能正常滚动

## 📝 注意事项

1. **数据字段匹配**：确保dataIndex与API返回的字段名完全匹配
2. **操作列渲染**：使用h函数创建的元素需要正确的事件绑定
3. **类型安全**：注意TypeScript类型检查
4. **样式继承**：确保原有的CSS样式仍然生效

---

**迁移时间**: 2025-06-20  
**迁移状态**: ✅ 完成  
**测试状态**: 🧪 待验证  
**影响范围**: 表格显示和交互功能
