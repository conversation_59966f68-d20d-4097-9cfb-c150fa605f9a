# 全局公司管理功能测试指南

## 功能概述

全局公司管理功能已成功集成到原始发票界面，实现了以下特性：

### ✅ 已实现的功能

1. **全局公司状态管理**
   - 使用 Pinia store (`useCompanySelectionStore`) 管理当前选中的公司
   - 与AI聊天窗口共享相同的全局状态

2. **公司列表获取**
   - 从AI聊天窗口的公司列表API获取可选公司
   - 页面初始化时自动获取公司列表

3. **默认值设置**
   - 公司名称字段自动填入当前全局选中的公司名称
   - 移除了必填验证警告，改为智能提示

4. **下拉选择框**
   - 将公司名称字段从输入框改为下拉选择框
   - 支持搜索和过滤功能

5. **状态同步**
   - 监听全局公司选择变化，自动刷新数据
   - 确保与AI聊天窗口的状态一致性

## 测试步骤

### 1. 基础功能测试

1. **页面初始化测试**
   ```
   1. 打开原始发票界面
   2. 检查公司名称下拉框是否显示可选公司列表
   3. 检查是否自动选中了全局当前公司
   4. 检查是否自动加载了对应公司的数据
   ```

2. **公司选择测试**
   ```
   1. 点击公司名称下拉框
   2. 选择不同的公司
   3. 检查数据是否自动刷新
   4. 检查全局状态是否更新
   ```

### 2. 状态同步测试

1. **与AI聊天窗口同步测试**
   ```
   1. 在AI聊天窗口选择公司A
   2. 切换到原始发票界面
   3. 检查公司名称是否自动更新为公司A
   4. 检查数据是否为公司A的数据
   ```

2. **反向同步测试**
   ```
   1. 在原始发票界面选择公司B
   2. 切换到AI聊天窗口
   3. 检查AI聊天窗口的公司选择是否更新为公司B
   ```

### 3. Tab切换测试

1. **不同Tab的公司状态**
   ```
   1. 在销项发票Tab选择公司A
   2. 切换到进项发票Tab
   3. 检查公司选择是否保持为公司A
   4. 切换到银行回单Tab
   5. 检查公司选择是否保持为公司A
   ```

### 4. 错误处理测试

1. **无公司选择测试**
   ```
   1. 清空公司选择（如果可能）
   2. 点击搜索按钮
   3. 检查是否显示"请选择公司名称"提示
   ```

2. **网络错误测试**
   ```
   1. 断开网络连接
   2. 刷新页面
   3. 检查是否有合适的错误处理
   ```

## 预期结果

### ✅ 正常情况

- 页面加载时自动获取公司列表
- 公司名称字段显示为下拉选择框
- 自动选中全局当前公司
- 选择公司后自动刷新数据
- 与AI聊天窗口状态保持同步

### ⚠️ 异常情况

- 获取公司列表失败时显示默认公司
- 无公司选择时显示友好提示
- 网络错误时显示错误信息

## 技术实现细节

### 关键组件

1. **useCompanySelection** - 公司选择逻辑复用
2. **useCompanySelectionStore** - 全局状态管理
3. **CompanyOption** - 公司选项类型定义

### 关键文件

- `src/views/original-voucher/index.vue` - 主界面组件
- `src/views/original-voucher/data.ts` - 表单schema定义
- `src/store/modules/company-selection.ts` - 全局状态store
- `src/components/ai-chat/composables/useCompanySelection.ts` - 公司选择逻辑

### 状态流程

```
1. 页面初始化 → 获取公司列表 → 设置默认公司 → 加载数据
2. 用户选择公司 → 更新全局状态 → 刷新数据
3. 全局状态变化 → 监听器触发 → 自动刷新数据
```

## 注意事项

1. 确保AI聊天窗口和原始发票界面使用相同的公司API
2. 公司选择变化时会自动刷新数据，可能会有短暂的加载状态
3. 全局状态使用localStorage持久化，页面刷新后会保持选择状态
