<script setup lang="ts">
  import type { CompanyOption } from './data';

  import type {
    BankReceiptData,
    BankReceiptQueryParams,
    InvoiceData,
    InvoiceQueryParams,
  } from '#/api/original-voucher';

  import { computed, h, onMounted, ref, watch } from 'vue';

  import { message } from 'ant-design-vue';

  import { useVbenForm } from '#/adapter/form';
  import {
    fetchBankReceiptList,
    fetchInvoiceList,
  } from '#/api/original-voucher/api-v2';
  import { useCompanySelection } from '#/components/ai-chat/composables/useCompanySelection';

  import { bankQuerySchema, invoiceQuerySchema } from './data';

  const tabList = [
    { key: 'output', label: '销项发票' },
    { key: 'input', label: '进项发票' },
    { key: 'bank', label: '银行回单' },
    { key: 'payroll', label: '工资单' },
  ];
  const activeTab = ref('output');

  // 动态schema和columns
  const formSchema = computed(() => {
    const currentCompany = selectedCompany.value;
    if (activeTab.value === 'bank') {
      return bankQuerySchema(companyOptions.value, currentCompany);
    }
    return invoiceQuerySchema(companyOptions.value, currentCompany);
  });

  // 数据源
  const tableData = ref<(BankReceiptData | InvoiceData)[]>([]);
  const loading = ref(false);

  // 全局公司状态管理
  const { companyList, fetchCompanyNames, selectedCompany } =
    useCompanySelection();

  // 公司选项
  const companyOptions = computed<CompanyOption[]>(() => {
    return companyList.value.map((company) => ({
      label: company.name,
      value: company.name,
    }));
  });

  // 用于强制刷新表单和表格
  const formKey = computed(() => `${activeTab.value}-form`);
  const tableKey = computed(() => `${activeTab.value}-table`);

  // 筛选表单 - 使用响应式schema
  const formOptions = computed(() => ({
    schema: formSchema.value,
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  }));

  const [BasicForm, formApi] = useVbenForm(formOptions.value);

  // Ant Design表格列配置
  const antdColumns = computed(() => {
    if (activeTab.value === 'bank') {
      return [
        {
          dataIndex: 'company_name',
          ellipsis: true,
          key: 'company_name',
          title: '公司名称',
          width: 180,
        },
        {
          dataIndex: 'transaction_time',
          key: 'transaction_time',
          title: '交易时间',
          width: 150,
        },
        {
          dataIndex: 'month',
          key: 'month',
          title: '月份',
          width: 80,
        },
        {
          dataIndex: 'type',
          key: 'type',
          title: '类型',
          width: 80,
        },
        {
          dataIndex: 'voucher_number',
          ellipsis: true,
          key: 'voucher_number',
          title: '凭证编号',
          width: 120,
        },
        {
          dataIndex: 'transaction_id',
          ellipsis: true,
          key: 'transaction_id',
          title: '交易流水号',
          width: 150,
        },
        {
          dataIndex: 'account_name',
          ellipsis: true,
          key: 'account_name',
          title: '账户名称',
          width: 180,
        },
        {
          dataIndex: 'bank_name',
          key: 'bank_name',
          title: '银行名称',
          width: 120,
        },
        {
          dataIndex: 'conterpary_account_name',
          ellipsis: true,
          key: 'conterpary_account_name',
          title: '对方账户名称',
          width: 150,
        },
        {
          align: 'right' as const,
          dataIndex: 'amount',
          key: 'amount',
          title: '金额',
          width: 120,
        },
        {
          dataIndex: 'currency',
          key: 'currency',
          title: '币种',
          width: 60,
        },
        {
          dataIndex: 'summary',
          ellipsis: true,
          key: 'summary',
          title: '摘要',
          width: 150,
        },
        {
          dataIndex: 'note',
          ellipsis: true,
          key: 'note',
          title: '备注',
          width: 120,
        },
        {
          customRender: ({ record }: any) => {
            return h(
              'a',
              {
                class: 'table-action-link',
                href: buildFileUrl(record),
                target: '_blank',
              },
              '查看原文件',
            );
          },
          fixed: 'right' as const,
          key: 'action',
          title: '操作',
          width: 120,
        },
      ];
    } else {
      // 发票列配置
      return [
        {
          dataIndex: 'digital_invoice_number',
          ellipsis: true,
          key: 'digital_invoice_number',
          title: '发票号码',
          width: 180,
        },
        {
          dataIndex: 'voucher_number',
          ellipsis: true,
          key: 'voucher_number',
          title: '凭证号',
          width: 120,
        },
        {
          dataIndex: 'status',
          key: 'status',
          title: '发票状态',
          width: 100,
        },
        {
          dataIndex: 'type',
          key: 'type',
          title: '发票类型',
          width: 120,
        },
        {
          dataIndex: 'issue_date',
          key: 'issue_date',
          title: '开票日期',
          width: 120,
        },
        {
          dataIndex: 'goods_name',
          ellipsis: true,
          key: 'goods_name',
          title: '货物或劳务明细',
          width: 200,
        },
        {
          dataIndex: 'seller_name',
          ellipsis: true,
          key: 'seller_name',
          title: '销方名称',
          width: 180,
        },
        {
          dataIndex: 'buyer_name',
          ellipsis: true,
          key: 'buyer_name',
          title: '购方名称',
          width: 180,
        },
        {
          customRender: ({ record }: any) => {
            return h(
              'a',
              {
                class: 'table-action-link',
                href: buildFileUrl(record),
                target: '_blank',
              },
              '查看原文件',
            );
          },
          fixed: 'right' as const,
          key: 'action',
          title: '操作',
          width: 120,
        },
      ];
    }
  });

  // 查询方法
  async function fetchData() {
    loading.value = true;
    try {
      const values = await formApi.getValues();

      if (activeTab.value === 'bank') {
        // 银行回单查询
        const [begin_time, end_time] = values.dateRange || [];
        const params: BankReceiptQueryParams = {
          begin_time,
          company_name: values.company_name || '',
          end_time,
          month: values.month,
          type: values.type,
          voucher_number: values.voucher_number,
        };

        // 如果没有公司名称，使用全局选中的公司
        if (!params.company_name) {
          params.company_name = selectedCompany.value;
        }

        // 如果仍然没有公司名称，提示用户
        if (!params.company_name) {
          message.warning('请选择公司名称');
          return;
        }

        // 过滤掉空值参数
        Object.keys(params).forEach((key) => {
          const value = params[key as keyof BankReceiptQueryParams];
          if (value === undefined || value === null || value === '') {
            delete params[key as keyof BankReceiptQueryParams];
          }
        });

        console.log('银行回单查询参数:', params);
        const result = await fetchBankReceiptList(params);

        if (result.success) {
          tableData.value = result.data || [];
          console.log(
            '获取银行回单数据成功:',
            result.data?.length || 0,
            '条记录',
          );
          console.log('银行回单数据:', result.data);

          // 打印原始响应用于调试
          if (result.originalResponse) {
            console.log('银行回单原始响应:', result.originalResponse);
          }
        } else {
          message.error(result.message || '获取银行回单数据失败');
          tableData.value = [];
        }
      } else {
        // 发票查询
        const [begin_time, end_time] = values.dateRange || [];
        const params: InvoiceQueryParams = {
          begin_time,
          company_name: values.company_name || '',
          end_time,
          input_output: activeTab.value as 'input' | 'output',
          status: values.status,
          voucher_number: values.voucher_number,
        };

        // 如果没有公司名称，使用全局选中的公司
        if (!params.company_name) {
          params.company_name = selectedCompany.value;
        }

        // 如果仍然没有公司名称，提示用户
        if (!params.company_name) {
          message.warning('请选择公司名称');
          return;
        }

        // 过滤掉空值参数
        Object.keys(params).forEach((key) => {
          const value = params[key as keyof InvoiceQueryParams];
          if (value === undefined || value === null || value === '') {
            delete params[key as keyof InvoiceQueryParams];
          }
        });

        console.log('发票查询参数:', params);
        const result = await fetchInvoiceList(params);

        if (result.success) {
          tableData.value = result.data || [];
          console.log('获取发票数据成功:', result.data?.length || 0, '条记录');
          console.log('发票数据:', result.data);

          // 打印原始响应用于调试
          if (result.originalResponse) {
            console.log('发票原始响应:', result.originalResponse);
          }
        } else {
          message.error(result.message || '获取发票数据失败');
          tableData.value = [];
        }
      }
    } catch (error: any) {
      console.error('请求失败:', error);
      message.error(error?.message || '请求失败');
      tableData.value = [];
    } finally {
      loading.value = false;
    }
  }

  // 页面初始化时获取公司列表和数据
  onMounted(async () => {
    try {
      // 先获取公司列表
      await fetchCompanyNames();
      console.log('公司列表获取完成:', companyList.value);

      // 等待一小段时间确保状态更新
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 然后获取数据
      await fetchData();
    } catch (error) {
      console.error('页面初始化失败:', error);
      // 即使获取公司列表失败，也尝试获取数据
      fetchData();
    }
  });

  // tab切换时自动刷新数据
  watch(activeTab, () => {
    fetchData();
  });

  // 监听全局公司选择变化
  watch(selectedCompany, (newCompany) => {
    console.log('全局公司选择变化:', newCompany);
    // 当全局公司变化时，自动刷新数据
    fetchData();
  });

  // 搜索按钮点击事件
  function handleSearch() {
    fetchData();
  }

  // 操作按钮
  function handleAdd() {
    message.info('新增加原始凭证功能开发中');
  }

  function handleAI() {
    message.info('AI记账功能开发中');
  }

  // 构建文件URL
  function buildFileUrl(row: any) {
    // 如果有url字段，直接使用
    if (row.url) {
      return row.url;
    }

    // 如果有source_file字段，构建URL
    if (row.source_file) {
      return `/files/${row.source_file}`;
    }

    // 默认返回空链接
    return '#';
  }
</script>

<template>
  <a-card class="voucher-card" :body-style="{ padding: '24px 24px 12px 24px' }">
    <div class="voucher-header">
      <a-tabs v-model:active-key="activeTab" class="voucher-tabs">
        <a-tab-pane v-for="tab in tabList" :key="tab.key" :tab="tab.label" />
      </a-tabs>
    </div>
    <div class="voucher-filter-row">
      <div class="voucher-filter-form">
        <BasicForm :key="formKey" />
      </div>
      <div class="voucher-filter-actions">
        <a-button type="primary" @click="handleSearch">搜索</a-button>
        <a-button class="ml-2" @click="handleAdd">新增加原始凭证</a-button>
        <a-button class="ai-btn ml-2" @click="handleAI">AI记账</a-button>
      </div>
    </div>
    <div class="voucher-table-wrap">
      <a-table
        :key="tableKey"
        :columns="antdColumns"
        :data-source="tableData"
        :loading="loading"
        :pagination="{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
        }"
        :scroll="{ x: 1200 }"
        row-key="_id"
        size="middle"
      />
    </div>
  </a-card>
</template>

<style scoped>
  .voucher-card {
    max-width: 1600px;
    margin: 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px #f0f1f2;
  }

  .voucher-header {
    margin-bottom: 8px;
  }

  .voucher-tabs {
    font-size: 16px;
  }

  .voucher-filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-end;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .voucher-filter-form {
    flex: 1 1 0%;
    min-width: 320px;
  }

  .voucher-filter-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: flex-end;
    min-width: 320px;
  }

  .ai-btn {
    color: #fff;
    background: linear-gradient(90deg, #4f8cff 0%, #6ad1ff 100%);
    border: none;
  }

  .voucher-table-wrap {
    margin-top: 8px;
  }

  .table-action-link {
    color: #1677ff;
    text-decoration: underline;
    cursor: pointer;
  }

  :deep(.ant-table) {
    font-size: 14px;
  }

  :deep(.ant-table-thead > tr > th) {
    font-weight: 600;
    background: #f7f8fa;
  }

  :deep(.ant-pagination) {
    justify-content: flex-end;
    margin-top: 16px;
  }
</style>
