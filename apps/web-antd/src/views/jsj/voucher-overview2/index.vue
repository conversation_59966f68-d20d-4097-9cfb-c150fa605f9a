<script setup lang="ts">
  import type { Voucher } from './types';

  import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

  import {
    DeleteOutlined,
    MinusOutlined,
    MoreOutlined,
    PlusOutlined,
    RightOutlined,
    SaveOutlined,
    SearchOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  import {
    getCurrentVouchers,
    getVoucherSourceData,
    mergeVouchers,
    updateVoucher,
  } from '#/api/tool/voucher';
  import { useCompanySelectionStore } from '#/store/modules/company-selection';
  import { useMonthSelectionStore } from '#/store/modules/month-selection';
  import { useVoucherStore } from '#/store/modules/voucher';

  import SourceDataDetail from './components/SourceDataDetail.vue';

  const voucherStore = useVoucherStore();
  const companySelectionStore = useCompanySelectionStore();
  const monthSelectionStore = useMonthSelectionStore();

  // 响应式数据
  const vouchers = ref<Voucher[]>([]);
  const searchText = ref('');
  const currentPage = ref(1);
  const pageSize = ref(20); // 默认改为20条，更适合大屏幕
  const jumpPage = ref(1);
  const detailModalVisible = ref(false);
  const selectedVoucher = ref<null | Voucher>(null);
  const detailData = ref<any>(null);
  const detailLoading = ref(false);
  const loading = ref(false);
  // 是否有从AI聊天生成的凭证数据
  const hasGeneratedVoucher = ref(false);
  // 选中的凭证ID列表（用于合并功能）
  const selectedVoucherIds = ref<Set<number | string>>(new Set());
  // 是否显示合并确认弹窗
  const mergeConfirmVisible = ref(false);
  // 合并操作加载状态
  const mergeLoading = ref(false);
  // 文件预览相关状态
  const filePreviewVisible = ref(false);
  const previewUrl = ref('');
  const previewTitle = ref('');
  const previewFiles = ref<any[]>([]);
  const currentFileIndex = ref(0);

  // 文件预览相关函数
  function previewFile(url: string, title: string = '') {
    previewUrl.value = url;
    previewTitle.value = title || '文件预览';
    filePreviewVisible.value = true;
  }

  function downloadFile(url: string, filename: string = '') {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || url.split('/').pop() || 'download';
    link.target = '_blank';
    document.body.append(link);
    link.click();
    link.remove();
  }

  function closeFilePreview() {
    filePreviewVisible.value = false;
    previewUrl.value = '';
    previewTitle.value = '';
    previewFiles.value = [];
    currentFileIndex.value = 0;
  }

  // 直接预览单个文件
  function handlePreviewFile(voucher: Voucher, fileIndex: number) {
    const files = getVoucherFiles(voucher);
    if (files.length > 0 && fileIndex < files.length) {
      // 设置文件列表用于导航
      previewFiles.value = files;
      currentFileIndex.value = fileIndex;
      // 预览文件
      previewFile(files[fileIndex].fullUrl, files[fileIndex].displayName);
    }
  }

  function getFileName(url: string): string {
    return url.split('/').pop() || '未知文件';
  }

  function getFileType(url: string): string {
    const ext = url.split('.').pop()?.toLowerCase() || '';
    if (['gif', 'jpeg', 'jpg', 'png', 'webp'].includes(ext)) {
      return 'image';
    } else if (ext === 'pdf') {
      return 'pdf';
    } else {
      return 'other';
    }
  }

  // 时间格式化函数
  function formatTimestamp(timestamp: string | undefined): string {
    if (!timestamp) return '';

    try {
      // 如果是时间戳（数字字符串），转换为毫秒
      const isNumericTimestamp = /^\d+$/.test(timestamp);
      const date = isNumericTimestamp
        ? new Date(Number.parseInt(timestamp) * 1000) // 假设是秒级时间戳
        : new Date(timestamp);

      if (Number.isNaN(date.getTime())) {
        return timestamp; // 如果无法解析，返回原始值
      }

      return date.toLocaleString('zh-CN', {
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        month: '2-digit',
        second: '2-digit',
        year: 'numeric',
      });
    } catch (error) {
      console.warn('时间格式化失败:', error);
      return timestamp || '';
    }
  }

  // 处理银行回单信息，兼容对象和数组格式
  function getBankReceiptInfo(voucher: Voucher) {
    const bankReceiptInfo = voucher.source_info?.bank_receipt_info;
    if (!bankReceiptInfo) return null;

    // 如果是数组格式（合并的凭证）
    if (Array.isArray(bankReceiptInfo)) {
      // 计算合并后的统计信息
      let totalIncomeAmount = 0;
      let totalExpenseAmount = 0;
      let incomeTransactionNum = 0;
      let expenseTransactionNum = 0;
      let latestTimestamp = '';

      bankReceiptInfo.forEach((receipt) => {
        const amount = receipt.amount || 0;
        if (amount > 0) {
          totalIncomeAmount += amount;
          incomeTransactionNum++;
        } else if (amount < 0) {
          totalExpenseAmount += Math.abs(amount);
          expenseTransactionNum++;
        }

        // 获取最新的时间戳
        if (
          receipt.transaction_time &&
          receipt.transaction_time > latestTimestamp
        ) {
          latestTimestamp = receipt.transaction_time;
        }
      });

      return {
        expense_transaction_num: expenseTransactionNum,
        income_transaction_num: incomeTransactionNum,
        is_merged: true,
        merged_count: bankReceiptInfo.length,
        timestamp: latestTimestamp || new Date().toISOString(),
        total_expense_amount: totalExpenseAmount,
        total_income_amount: totalIncomeAmount,
      };
    }

    // 如果是对象格式（普通凭证）
    return {
      ...bankReceiptInfo,
      is_merged: false,
      merged_count: 1,
    };
  }

  // 计算最佳页面大小
  function calculateOptimalPageSize(): number {
    const windowHeight = window.innerHeight;
    const headerHeight = 120; // 顶部操作栏高度
    const footerHeight = 80; // 底部分页栏高度
    const tableHeaderHeight = 40; // 表格头部高度
    const voucherRowHeight = 120; // 每个凭证大约的高度（包含明细）

    const availableHeight =
      windowHeight - headerHeight - footerHeight - tableHeaderHeight;
    const estimatedRows = Math.floor(availableHeight / voucherRowHeight);

    // 确保至少显示5条，最多50条
    return Math.max(5, Math.min(50, estimatedRows));
  }

  // 智能分页大小选项
  const smartPageSizeOptions = computed(() => {
    const optimal = calculateOptimalPageSize();
    const options = [
      { label: '10 条/页', value: 10 },
      { label: '20 条/页', value: 20 },
      { label: '30 条/页', value: 30 },
      { label: '50 条/页', value: 50 },
      { label: '100 条/页', value: 100 },
    ];

    // 添加智能推荐选项
    if (options.some((opt) => opt.value === optimal)) {
      // 标记推荐选项
      const optimalOption = options.find((opt) => opt.value === optimal);
      if (optimalOption) {
        optimalOption.label = `${optimal} 条/页 (推荐)`;
      }
    } else {
      options.splice(2, 0, {
        label: `${optimal} 条/页 (推荐)`,
        value: optimal,
      });
    }

    return options.sort((a, b) => a.value - b.value);
  });

  // 编辑状态管理
  const editingDetail = ref<null | {
    detailIndex: number;
    voucherId: number | string;
  }>(null);
  const editingField = ref<null | {
    detailIndex: number;
    field: string;
    voucherId: number | string;
  }>(null);

  // 标记是否正在切换编辑字段
  const isSwitchingField = ref(false);

  // 取消编辑
  function cancelEdit() {
    console.log('取消编辑');
    editingField.value = null;
    editingDetail.value = null;
  }

  // 开始编辑字段
  async function startEditField(
    voucherId: number | string,
    detailIndex: number,
    field: string,
  ) {
    // 标记正在切换字段，防止失去焦点时误触发完成编辑
    isSwitchingField.value = true;

    editingField.value = {
      detailIndex,
      field,
      voucherId,
    };

    // 等待DOM更新后手动聚焦
    await nextTick();

    // 查找当前编辑的输入框并聚焦
    const inputElement = document.querySelector(
      '.editing-field input',
    ) as HTMLInputElement;
    const numberInputElement = document.querySelector(
      '.editing-field .ant-input-number-input',
    ) as HTMLInputElement;

    if (inputElement) {
      inputElement.focus();
      inputElement.select();
    } else if (numberInputElement) {
      numberInputElement.focus();
      numberInputElement.select();
    }

    // 重置切换标志
    setTimeout(() => {
      isSwitchingField.value = false;
    }, 200);
  }

  // 完成编辑（保存并退出）
  function finishEdit() {
    editingField.value = null;
  }

  // 处理输入框的键盘事件
  function handleInputKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      event.preventDefault();
      event.stopPropagation();
      cancelEdit();
    } else if (event.key === 'Enter') {
      event.preventDefault();
      event.stopPropagation();
      finishEdit();
    }
  }

  // 处理输入框失去焦点
  function handleInputBlur() {
    // 如果正在切换字段，不要触发完成编辑
    if (isSwitchingField.value) {
      return;
    }

    // 使用短暂延时，检查是否是切换到另一个编辑字段
    setTimeout(() => {
      if (editingField.value && !isSwitchingField.value) {
        // 检查当前焦点是否在另一个可编辑元素上
        const activeElement = document.activeElement;
        const isEditableElement =
          activeElement &&
          (activeElement.classList.contains('ant-input') ||
            activeElement.classList.contains('ant-input-number-input') ||
            activeElement.closest('.editing-field') ||
            activeElement.closest('.summary-text') ||
            activeElement.closest('.account-text') ||
            activeElement.closest('.amount-text'));

        // 如果焦点不在可编辑元素上，则完成编辑
        if (!isEditableElement) {
          finishEdit();
        }
      }
    }, 150); // 增加延时，确保新的焦点已经设置
  }

  // 全局键盘事件处理（ESC键）
  function handleGlobalKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape' && editingField.value) {
      console.log('全局ESC键取消编辑');
      cancelEdit();
      event.preventDefault();
      event.stopPropagation();
    }
  }

  // 从API加载凭证数据
  async function loadVouchersFromAPI() {
    try {
      loading.value = true;

      // 获取当前选中的公司和月份
      const companyName = companySelectionStore.getSelectedCompany();
      const month = monthSelectionStore.getFormattedMonth();

      console.log('加载凭证数据:', { companyName, month });

      const response = await getCurrentVouchers(companyName, month);

      console.log('API响应:', response);

      // 转换API数据为组件所需的格式
      const apiVouchers = (response as any).data.data.items.map(
        (item: any, index: number) => ({
          confirmed: item.confirmed,
          details: item.voucher.details.map((detail: any) => ({
            account: detail.account,
            credit: detail.credit,
            debit: detail.debit,
            summary: detail.summary,
          })),
          executor: item.executor,
          id: item.voucher.unique_id || `voucher_${index}`,
          is_generated: false, // API返回的数据不是生成的
          originId: item.voucher.id,
          record_date: item.voucher.record_date,
          reviewed: item.confirmed, // 使用confirmed状态作为reviewed状态
          source_info: item.source_info,
          source_type: item.source_type,
          type: item.voucher.type,
        }),
      );

      // 按创建时间或ID降序排列，最新的在前面
      const sortedVouchers = apiVouchers.sort((a: any, b: any) => {
        // 优先按记录日期排序，如果没有记录日期则按ID排序
        if (a.record_date && b.record_date) {
          return (
            new Date(b.record_date).getTime() -
            new Date(a.record_date).getTime()
          );
        }
        // 如果没有记录日期，按ID降序排列（假设ID越大越新）
        return String(b.id).localeCompare(String(a.id));
      });

      vouchers.value = sortedVouchers;
      message.success(`成功加载 ${apiVouchers.length} 条凭证数据`);
    } catch (error) {
      console.error('加载凭证数据失败:', error);
      message.error('加载凭证数据失败，请重试');
    } finally {
      loading.value = false;
    }
  }

  // 应用生成的凭证
  function applyGeneratedVouchers() {
    // 这里实现将生成的凭证应用到系统中的逻辑
    // 可以调用API将凭证数据保存到数据库

    // 找出所有标记为生成的凭证
    const generatedVouchers = vouchers.value.filter((v) => v.is_generated);

    if (generatedVouchers.length > 0) {
      message.success('凭证已应用到系统中');

      // 移除生成标记，使其成为正常凭证
      generatedVouchers.forEach((v) => {
        if (v.is_generated) {
          v.is_generated = false;
        }
      });

      // 清除生成的凭证数据
      voucherStore.clearGeneratedVoucherData();
      voucherStore.clearBankReceiptData();
      hasGeneratedVoucher.value = false;
    }
  }

  // 清除生成的凭证
  function clearGeneratedVouchers() {
    // 移除所有标记为生成的凭证
    vouchers.value = vouchers.value.filter((v) => !v.is_generated);

    // 清除生成的凭证数据
    voucherStore.clearGeneratedVoucherData();
    voucherStore.clearBankReceiptData();
    hasGeneratedVoucher.value = false;

    message.info('已清除AI生成的凭证');
  }

  // 计算属性
  const totalVouchers = computed(() => vouchers.value.length);
  const totalPages = computed(() =>
    Math.ceil(totalVouchers.value / pageSize.value),
  );

  const paginatedVouchers = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return vouchers.value.slice(start, end);
  });

  // 合并相关计算属性
  const selectedVouchers = computed(() => {
    return vouchers.value.filter((voucher) =>
      selectedVoucherIds.value.has(voucher.id),
    );
  });

  const canMergeVouchers = computed(() => {
    const selected = selectedVouchers.value;
    // 至少选择2个凭证
    if (selected.length < 2) return false;
    // 只能合并银行回单类型的凭证
    return selected.every((voucher) => voucher.source_type === '银行回单');
  });

  const isAllSelected = computed(() => {
    const currentPageVouchers = paginatedVouchers.value;
    return (
      currentPageVouchers.length > 0 &&
      currentPageVouchers.every((voucher) =>
        selectedVoucherIds.value.has(voucher.id),
      )
    );
  });

  const isIndeterminate = computed(() => {
    const currentPageVouchers = paginatedVouchers.value;
    const selectedCount = currentPageVouchers.filter((voucher) =>
      selectedVoucherIds.value.has(voucher.id),
    ).length;
    return selectedCount > 0 && selectedCount < currentPageVouchers.length;
  });

  // 更新本地凭证数据
  function updateLocalVoucher(updatedVoucher: Voucher) {
    const index = vouchers.value.findIndex((v) => v.id === updatedVoucher.id);
    if (index !== -1) {
      vouchers.value[index] = updatedVoucher;
    }
  }

  // 格式化数字
  function formatNumber(num: number): string {
    if (num === undefined) {
      return '0.00';
    }
    return num.toLocaleString('zh-CN', {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    });
  }

  // 计算借方总额
  function getTotalDebit(voucher: Voucher): number {
    return voucher.details.reduce(
      (sum, detail) => sum + (detail.debit || 0),
      0,
    );
  }

  // 计算贷方总额
  function getTotalCredit(voucher: Voucher): number {
    return voucher.details.reduce(
      (sum, detail) => sum + (detail.credit || 0),
      0,
    );
  }

  // 合并相关函数
  function handleVoucherSelect(voucherId: number | string, checked: boolean) {
    if (checked) {
      selectedVoucherIds.value.add(voucherId);
    } else {
      selectedVoucherIds.value.delete(voucherId);
    }
  }

  function handleSelectAll(e: any) {
    const checked = e.target.checked;
    const currentPageVouchers = paginatedVouchers.value;
    if (checked) {
      currentPageVouchers.forEach((voucher) => {
        selectedVoucherIds.value.add(voucher.id);
      });
    } else {
      currentPageVouchers.forEach((voucher) => {
        selectedVoucherIds.value.delete(voucher.id);
      });
    }
  }

  function clearSelection() {
    selectedVoucherIds.value.clear();
  }

  function handleMergeVouchers() {
    if (!canMergeVouchers.value) {
      message.warning('请选择至少2个银行回单类型的凭证进行合并');
      return;
    }
    mergeConfirmVisible.value = true;
  }

  async function confirmMergeVouchers() {
    try {
      mergeLoading.value = true;

      const companyName = companySelectionStore.getSelectedCompany();
      const month = monthSelectionStore.getFormattedMonth();

      const voucherUniqueIds = selectedVouchers.value.map((voucher) =>
        String(voucher.id),
      );

      console.log('合并凭证请求:', { companyName, month, voucherUniqueIds });

      const response = await mergeVouchers({
        company_name: companyName,
        month,
        voucher_unique_ids: voucherUniqueIds,
      });

      console.log('合并凭证响应:', response);

      const result = (response as any).data;

      if (result.result === '成功') {
        // 获取合并的凭证数量，兼容不同的数据结构
        let mergedCount = selectedVouchers.value.length;
        try {
          if (result.merged_info?.source_info?.bank_receipt_info) {
            const bankReceiptInfo =
              result.merged_info.source_info.bank_receipt_info;
            if (Array.isArray(bankReceiptInfo)) {
              mergedCount = bankReceiptInfo.length;
            } else if (bankReceiptInfo.merged_voucher_count) {
              mergedCount = bankReceiptInfo.merged_voucher_count;
            }
          }
        } catch (error) {
          console.warn('获取合并数量失败，使用默认值:', error);
        }

        message.success(`成功合并 ${mergedCount} 个凭证`);

        // 清除选择状态
        clearSelection();

        // 重新加载凭证数据
        await loadVouchersFromAPI();

        // 关闭确认弹窗
        mergeConfirmVisible.value = false;
      } else {
        message.error(result.err_msg || '合并凭证失败');
      }
    } catch (error) {
      console.error('合并凭证失败:', error);
      message.error('合并凭证失败，请重试');
    } finally {
      mergeLoading.value = false;
    }
  }

  function cancelMergeVouchers() {
    mergeConfirmVisible.value = false;
  }

  // 事件处理
  function handleSearch() {
    // 搜索逻辑
  }

  // 构建完整的文件URL
  function buildFileUrl(
    filename: string,
    isBankReceipt: boolean = false,
  ): string {
    const companyName = companySelectionStore.getSelectedCompany();
    const baseUrl = 'http://**************:30081/prod-api/autojob/files';
    // 对公司名称进行URL编码
    const encodedCompanyName = encodeURIComponent(companyName);

    // 如果是银行回单的原始文件，在公司名称后面加上当前选中的月份
    if (isBankReceipt) {
      const currentMonth = monthSelectionStore.getFormattedMonth();
      return `${baseUrl}/${encodedCompanyName}/${currentMonth}/${filename}`;
    }

    return `${baseUrl}/${encodedCompanyName}/${filename}`;
  }

  // 获取凭证的所有原始文件
  function getVoucherFiles(voucher: Voucher): any[] {
    const sourceInfo = voucher.source_info;
    let files: any[] = [];
    let isBankReceipt = false;

    // 根据不同类型的凭证获取原始文件
    if (
      sourceInfo.bank_receipt_info &&
      !Array.isArray(sourceInfo.bank_receipt_info) &&
      (sourceInfo.bank_receipt_info as any).orign_files
    ) {
      files = (sourceInfo.bank_receipt_info as any).orign_files;
      isBankReceipt = true; // 标记为银行回单文件
    } else if (
      sourceInfo.input_invoice &&
      sourceInfo.input_invoice.length > 0
    ) {
      // 进项发票
      sourceInfo.input_invoice.forEach((invoice: any) => {
        if (invoice.orign_files && invoice.orign_files.length > 0) {
          files.push(...invoice.orign_files);
        }
      });
    } else if (
      sourceInfo.output_invoice &&
      sourceInfo.output_invoice.length > 0
    ) {
      // 销项发票
      sourceInfo.output_invoice.forEach((invoice: any) => {
        if (invoice.orign_files && invoice.orign_files.length > 0) {
          files.push(...invoice.orign_files);
        }
      });
    }

    // 构建完整的文件URL
    return files.map((file) => ({
      ...file,
      displayName: file.desc || getFileName(file.url), // 优先使用desc作为显示名称
      fullUrl: buildFileUrl(file.url, isBankReceipt),
    }));
  }

  async function handleViewDetails(voucher: Voucher) {
    console.log('🔍 点击查看详情:', voucher.id, voucher.source_type);
    selectedVoucher.value = voucher;
    detailModalVisible.value = true;
    detailLoading.value = true;
    detailData.value = null;

    try {
      // 调用新的API获取凭证原始数据
      const companyName = companySelectionStore.getSelectedCompany();
      const month = monthSelectionStore.getFormattedMonth();
      const voucherUniqueId =
        typeof voucher.id === 'string' ? voucher.id : String(voucher.id);

      console.log('获取凭证原始数据:', { companyName, month, voucherUniqueId });

      const response = await getVoucherSourceData(
        companyName,
        month,
        voucherUniqueId,
      );
      console.log('获取凭证原始数据response', response);
      const res = (response as any).data.data;
      if (res && res.source_info) {
        detailData.value = res.source_info;
        console.log('凭证原始数据:', detailData.value);
      } else {
        message.error('获取凭证原始数据失败');
      }
    } catch (error) {
      console.error('获取凭证原始数据失败:', error);
      message.error('获取凭证原始数据失败，请重试');
    } finally {
      detailLoading.value = false;
    }
  }

  // 删除凭证明细
  function handleDeleteDetail(voucherId: number | string, detailIndex: number) {
    const voucher = vouchers.value.find((v) => v.id === voucherId);
    if (!voucher) {
      message.error('找不到对应的凭证');
      return;
    }

    if (voucher.details.length <= 1) {
      message.error('凭证至少需要一条明细');
      return;
    }

    voucher.details.splice(detailIndex, 1);
    message.success('删除明细成功');
  }

  // 添加新的凭证明细
  function handleAddDetail(voucherId: number | string) {
    const voucher = vouchers.value.find((v) => v.id === voucherId);
    if (!voucher) {
      message.error('找不到对应的凭证');
      return;
    }

    const newDetail = {
      account: '',
      credit: 0,
      debit: 0,
      summary: '',
    };

    voucher.details.push(newDetail);

    // 自动进入编辑模式
    const newIndex = voucher.details.length - 1;
    editingDetail.value = { detailIndex: newIndex, voucherId };

    message.success('添加明细成功');
  }

  // 保存凭证修改
  async function handleSaveVoucher(voucher: Voucher) {
    try {
      // 验证借贷平衡
      const totalDebit = getTotalDebit(voucher);
      const totalCredit = getTotalCredit(voucher);

      if (Math.abs(totalDebit - totalCredit) > 0.01) {
        message.error('借贷不平衡，请检查明细');
        return;
      }

      // 验证必填字段
      for (const detail of voucher.details) {
        if (!detail.summary.trim()) {
          message.error('摘要不能为空');
          return;
        }
        if (!detail.account.trim()) {
          message.error('科目不能为空');
          return;
        }
      }

      const companyName = companySelectionStore.getSelectedCompany();
      const month = monthSelectionStore.getFormattedMonth();

      const updateData = {
        company_name: companyName,
        month,
        voucher: {
          details: voucher.details.map((detail, index) => ({
            account: detail.account,
            credit: detail.credit,
            debit: detail.debit,
            id: index + 1,
            summary: detail.summary,
          })),
          id:
            typeof voucher.id === 'string'
              ? Number.parseInt(voucher.id)
              : voucher.id,
          record_date: voucher.record_date,
          type: voucher.type,
        },
        voucher_unique_id: String(voucher.id),
      };

      await updateVoucher(updateData);

      // 更新本地数据
      updateLocalVoucher(voucher);

      // 清除编辑状态
      editingDetail.value = null;
      editingField.value = null;

      message.success('保存凭证成功');
    } catch (error) {
      console.error('保存凭证失败:', error);
      message.error('保存凭证失败，请重试');
    }
  }

  function handlePageChange(page: number) {
    if (page > 0 && page <= totalPages.value) {
      currentPage.value = page;
    }
  }

  function handlePageSizeChange(newPageSize: any) {
    pageSize.value = Number(newPageSize);
    currentPage.value = 1;
  }

  // 智能页面大小调整
  function applyOptimalPageSize() {
    const optimal = calculateOptimalPageSize();
    pageSize.value = optimal;
    currentPage.value = 1;
    message.success(`已调整为最佳页面大小：${optimal} 条/页`);
  }

  // 快速跳转到第一页/最后一页
  function jumpToFirstPage() {
    currentPage.value = 1;
  }

  function jumpToLastPage() {
    currentPage.value = totalPages.value;
  }

  // 生成页码数组（用于显示页码按钮）
  const visiblePageNumbers = computed(() => {
    const total = totalPages.value;
    const current = currentPage.value;
    const pages: number[] = [];

    if (total <= 7) {
      // 总页数少于等于7页，显示所有页码
      for (let i = 1; i <= total; i++) {
        pages.push(i);
      }
    } else {
      // 总页数大于7页，智能显示页码
      if (current <= 4) {
        // 当前页在前面
        pages.push(1, 2, 3, 4, 5, -1, total); // -1 表示省略号
      } else if (current >= total - 3) {
        // 当前页在后面
        pages.push(1, -1, total - 4, total - 3, total - 2, total - 1, total);
      } else {
        // 当前页在中间
        pages.push(1, -1, current - 1, current, current + 1, -1, total);
      }
    }

    return pages;
  });

  function handleJumpToPage() {
    if (
      jumpPage.value &&
      jumpPage.value >= 1 &&
      jumpPage.value <= totalPages.value
    ) {
      currentPage.value = jumpPage.value;
    }
  }

  // 初始化最佳页面大小
  function initializeOptimalPageSize() {
    const optimal = calculateOptimalPageSize();
    // 只在首次加载时自动设置，避免覆盖用户的选择
    if (pageSize.value === 20) {
      // 默认值
      // pageSize.value = optimal;
      pageSize.value = 100;
    }
  }

  // 处理重新加载凭证数据的事件
  function handleReloadVoucherData() {
    console.log('收到重新加载凭证数据的事件');
    loadVouchersFromAPI();
  }

  // 生命周期
  onMounted(async () => {
    // 初始化最佳页面大小
    initializeOptimalPageSize();

    // 首先从API加载凭证数据
    await loadVouchersFromAPI();

    // 检查是否有从AI聊天生成的凭证数据（保留原有逻辑作为备用）
    // checkGeneratedVoucherData();

    // 添加全局键盘事件监听器
    document.addEventListener('keydown', handleGlobalKeydown);

    // 添加重新加载凭证数据的事件监听器
    window.addEventListener('reload-voucher-data', handleReloadVoucherData);

    // 设置一个短暂的延时，确保DOM已经更新
    setTimeout(() => {
      // 如果有生成的凭证，滚动到第一个生成的凭证位置
      if (hasGeneratedVoucher.value) {
        scrollToGeneratedVoucher();
      }
    }, 500);
  });

  // 组件卸载时移除事件监听器
  onUnmounted(() => {
    document.removeEventListener('keydown', handleGlobalKeydown);
    // 移除重新加载凭证数据的事件监听器
    window.removeEventListener('reload-voucher-data', handleReloadVoucherData);
  });

  // 滚动到生成的凭证位置
  function scrollToGeneratedVoucher() {
    // 查找第一个AI生成的凭证元素
    const generatedVoucherElement = document.querySelector(
      '.voucher-header-row.ai-generated',
    );
    if (generatedVoucherElement) {
      // 滚动到该元素位置，并添加一些偏移以确保完全可见
      generatedVoucherElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  }

  // 监听生成的凭证数据变化
  watch(
    () => voucherStore.generatedVoucherData,
    (newVal) => {
      if (newVal) {
        hasGeneratedVoucher.value = true;
        // 处理生成的凭证数据，将其添加到凭证列表中
        addGeneratedVouchersToList();

        // 设置一个短暂的延时，确保DOM已经更新
        setTimeout(() => {
          // 滚动到生成的凭证位置
          scrollToGeneratedVoucher();
        }, 300);
      }
    },
  );

  // 将生成的凭证数据添加到凭证列表中
  function addGeneratedVouchersToList() {
    if (
      !voucherStore.generatedVoucherData ||
      !voucherStore.generatedVoucherData.generated_vouchers
    ) {
      return;
    }

    // 转换生成的凭证数据为凭证列表所需的格式
    const generatedVouchers =
      voucherStore.generatedVoucherData.generated_vouchers.map(
        (genVoucher, index) => {
          // 创建一个新的凭证对象
          return {
            details: genVoucher.details.map((detail) => ({
              account: `${detail.account_code} ${detail.account_name}`,
              credit:
                typeof detail.credit === 'string'
                  ? Number.parseFloat(detail.credit) || 0
                  : detail.credit || 0,
              debit:
                typeof detail.debit === 'string'
                  ? Number.parseFloat(detail.debit) || 0
                  : detail.debit || 0,
              summary: detail.summary,
            })),
            executor: 'llm' as 'history' | 'llm' | 'people', // 标记为AI生成
            id: `gen_${Date.now()}_${index}`, // 生成临时ID
            is_generated: true, // 标记为生成的凭证
            record_date: (genVoucher.date ||
              new Date().toISOString().split('T')[0]) as string,
            reviewed: false, // 新生成的凭证未审核
            source_info: {
              bank_receipt_info: voucherStore.bankReceiptData
                ? {
                    expense_transaction_num:
                      Number.parseFloat(voucherStore.bankReceiptData.amount) < 0
                        ? 1
                        : 0,
                    income_transaction_num:
                      Number.parseFloat(voucherStore.bankReceiptData.amount) > 0
                        ? 1
                        : 0,
                    months: [
                      voucherStore.bankReceiptData.transaction_date
                        ? voucherStore.bankReceiptData.transaction_date.slice(
                            0,
                            7,
                          )
                        : '',
                    ],
                    total_expense_amount:
                      Number.parseFloat(voucherStore.bankReceiptData.amount) < 0
                        ? Math.abs(
                            Number.parseFloat(
                              voucherStore.bankReceiptData.amount,
                            ),
                          )
                        : 0,
                    total_income_amount: Math.max(
                      Number.parseFloat(voucherStore.bankReceiptData.amount),
                      0,
                    ),
                  }
                : {
                    expense_transaction_num: 0,
                    income_transaction_num: 0,
                    months: [],
                    total_expense_amount: 0,
                    total_income_amount: 0,
                  },
            },
            source_type: '银行回单' as
              | '工资单'
              | '进项发票'
              | '银行回单'
              | '销项发票',
            type:
              (genVoucher.voucher_type as '借' | '结' | '记' | '转') || '记',
          };
        },
      );

    // 将生成的凭证添加到凭证列表的开头
    vouchers.value = [...generatedVouchers, ...vouchers.value];

    // 如果当前不在第一页，则跳转到第一页以显示生成的凭证
    if (currentPage.value !== 1) {
      currentPage.value = 1;
    }

    // 显示提示消息
    message.success(
      `已将${generatedVouchers.length}条AI生成的凭证添加到列表中`,
    );
  }
</script>

<template>
  <div class="voucher-overview">
    <!-- AI生成凭证提示 -->
    <div v-if="hasGeneratedVoucher" class="ai-generated-notice">
      <a-alert
        type="info"
        show-icon
        message="AI已生成凭证数据"
        description="从银行回单生成的凭证数据已添加到凭证列表中。"
      >
        <template #action>
          <div class="ai-generated-actions">
            <a-button
              type="primary"
              size="small"
              @click="applyGeneratedVouchers"
            >
              应用到系统
            </a-button>
            <a-button size="small" @click="clearGeneratedVouchers">
              清除
            </a-button>
          </div>
        </template>
      </a-alert>
    </div>
    <!-- 顶部操作栏 -->
    <div class="top-header">
      <!-- 左侧：搜索和更多查询 -->
      <div class="header-left">
        <div class="search-container">
          <a-input
            v-model:value="searchText"
            placeholder="请输入摘要"
            class="search-input"
            @change="handleSearch"
          >
            <template #prefix>
              <SearchOutlined class="search-icon" />
            </template>
          </a-input>
        </div>
        <a-button class="more-query-btn">
          更多查询
          <RightOutlined />
        </a-button>
      </div>

      <!-- 右侧：全局操作按钮 -->
      <div class="header-right">
        <a-button>新增凭证</a-button>
        <a-button>复制</a-button>
        <a-button>删除</a-button>
        <a-button
          type="primary"
          :disabled="!canMergeVouchers"
          :loading="mergeLoading"
          @click="handleMergeVouchers"
        >
          合并凭证 ({{ selectedVouchers.length }})
        </a-button>
        <a-button>打印</a-button>
        <a-button>导出</a-button>
        <a-button>
          更多
          <MoreOutlined />
        </a-button>
      </div>
    </div>

    <!-- 主表格 -->
    <div class="table-container">
      <table class="voucher-table">
        <thead>
          <tr>
            <th class="col-merged">
              <div class="merged-header">
                <a-checkbox
                  :checked="isAllSelected"
                  :indeterminate="isIndeterminate"
                  @change="handleSelectAll"
                  class="header-checkbox"
                >
                  选择
                </a-checkbox>
                <span class="header-divider">|</span>
                <span class="header-source">原始信息</span>
              </div>
            </th>
            <th class="col-summary">摘要</th>
            <th class="col-account">科目</th>
            <th class="col-debit">借方金额</th>
            <th class="col-credit">贷方金额</th>
            <th class="col-actions">操作</th>
          </tr>
        </thead>
        <tbody>
          <template v-for="voucher in paginatedVouchers" :key="voucher.id">
            <!-- 凭证头部信息行 -->
            <tr
              class="voucher-header-row"
              :class="{ 'ai-generated': voucher.is_generated }"
            >
              <td class="voucher-checkbox-cell">
                <a-checkbox
                  :checked="selectedVoucherIds.has(voucher.id)"
                  :disabled="voucher.source_type !== '银行回单'"
                  @change="
                    (e: any) =>
                      handleVoucherSelect(voucher.id, e.target.checked)
                  "
                />
              </td>
              <td colspan="5" class="voucher-header-cell">
                <div class="voucher-header-content">
                  <div class="voucher-info">
                    <span class="voucher-date">{{ voucher.record_date }}</span>
                    <span class="voucher-number">
                      {{ voucher.type }} -
                      {{ String(voucher.originId).padStart(3, '0') }}
                    </span>
                    <span v-if="voucher.executor === 'ai'" class="ai-badge">
                      AI
                    </span>
                  </div>
                  <div class="voucher-actions">
                    <a-button
                      size="small"
                      type="text"
                      class="action-btn"
                      @click="handleAddDetail(voucher.id)"
                    >
                      <PlusOutlined />
                      插入
                    </a-button>
                    <a-button size="small" type="text" class="action-btn">
                      <MinusOutlined />
                      红冲
                    </a-button>
                    <a-button
                      size="small"
                      type="text"
                      class="action-btn"
                      @click="handleSaveVoucher(voucher)"
                    >
                      <SaveOutlined />
                      保存
                    </a-button>
                  </div>
                </div>
              </td>
            </tr>

            <!-- 凭证明细行 -->
            <tr
              v-for="(detail, detailIndex) in voucher.details"
              :key="`${voucher.id}-${detailIndex}`"
              class="detail-row"
              :class="{ 'ai-generated': voucher.is_generated }"
            >
              <!-- 原始信息列（只在第一行显示，合并复选框列和原始信息列） -->
              <td
                v-if="detailIndex === 0"
                :rowspan="voucher.details.length"
                class="source-info-cell merged-cell"
              >
                <div class="source-info">
                  <div class="source-type">
                    <div class="source-title-section">
                      <span class="source-title">
                        {{ voucher.source_type }}
                      </span>
                    </div>
                    <div class="source-actions">
                      <a-button
                        type="link"
                        size="small"
                        @click="handleViewDetails(voucher)"
                        class="details-link"
                      >
                        详情
                      </a-button>
                    </div>
                  </div>
                  <div class="source-content">
                    <template v-if="voucher.source_info.invoice_info">
                      <div class="source-item">
                        {{ voucher.source_info.invoice_info.fund_desc }}
                      </div>
                      <!-- 发票金额信息一行两个显示 -->
                      <div class="source-item-row">
                        <div class="source-item-half">
                          金额/税: ¥{{
                            formatNumber(
                              voucher.source_info.invoice_info.amount,
                            )
                          }}/¥{{
                            formatNumber(voucher.source_info.invoice_info.tax)
                          }}
                        </div>
                        <div class="source-item-half">
                          合计: ¥{{
                            formatNumber(voucher.source_info.invoice_info.total)
                          }}
                        </div>
                      </div>
                    </template>
                    <template v-else-if="voucher.source_info.bank_receipt_info">
                      <template v-if="getBankReceiptInfo(voucher)">
                        <!-- 收入支出信息一行两个显示 -->
                        <div class="source-item-row">
                          <div class="source-item-half">
                            总收入: ¥{{
                              formatNumber(
                                getBankReceiptInfo(voucher)
                                  ?.total_income_amount || 0,
                              )
                            }}
                            <span
                              v-if="getBankReceiptInfo(voucher)?.is_merged"
                              class="merged-badge"
                            >
                              合并({{
                                getBankReceiptInfo(voucher)?.merged_count || 0
                              }}个)
                            </span>
                          </div>
                          <div class="source-item-half">
                            总支出: ¥{{
                              formatNumber(
                                getBankReceiptInfo(voucher)
                                  ?.total_expense_amount || 0,
                              )
                            }}
                          </div>
                        </div>
                        <!-- <div class="source-item">
                          时间:
                          {{
                            formatTimestamp(
                              getBankReceiptInfo(voucher)?.timestamp || '',
                            )
                          }}
                        </div> -->
                      </template>
                    </template>
                    <template v-else-if="voucher.source_info.payroll_info">
                      <!-- 工资信息一行两个显示 -->
                      <div class="source-item-row">
                        <div class="source-item-half">
                          总工资: ¥{{
                            formatNumber(
                              voucher.source_info.payroll_info
                                .total_gross_salary,
                            )
                          }}
                        </div>
                        <div class="source-item-half">
                          雇主缴费: ¥{{
                            formatNumber(
                              voucher.source_info.payroll_info
                                .total_employer_contributions,
                            )
                          }}
                        </div>
                      </div>
                      <div class="source-item-row">
                        <div class="source-item-half">
                          员工扣款: ¥{{
                            formatNumber(
                              voucher.source_info.payroll_info
                                .total_employee_deductions,
                            )
                          }}
                        </div>
                        <div class="source-item-half">
                          时间:
                          {{
                            formatTimestamp(
                              voucher.source_info.payroll_info.timestamp,
                            )
                          }}
                        </div>
                      </div>
                    </template>
                  </div>

                  <!-- 原始文件列表 -->
                  <div
                    v-if="getVoucherFiles(voucher).length > 0"
                    class="original-files-section"
                  >
                    <div class="files-header">
                      <span class="files-title">
                        原始文件 ({{ getVoucherFiles(voucher).length }})
                      </span>
                    </div>
                    <div class="files-list">
                      <div
                        v-for="(file, index) in getVoucherFiles(voucher)"
                        :key="index"
                        class="file-item-inline"
                        @click="handlePreviewFile(voucher, index)"
                      >
                        <div class="file-icon-small">
                          <span v-if="getFileType(file.url) === 'pdf'">📄</span>
                          <span v-else-if="getFileType(file.url) === 'image'">
                            🖼️
                          </span>
                          <span v-else>📎</span>
                        </div>
                        <div class="file-name-small" :title="file.displayName">
                          {{ file.displayName }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </td>

              <!-- 摘要列 -->
              <td class="summary-cell">
                <div
                  v-if="
                    editingField?.voucherId === voucher.id &&
                    editingField?.detailIndex === detailIndex &&
                    editingField?.field === 'summary'
                  "
                  class="editing-field"
                >
                  <a-input
                    v-model:value="detail.summary"
                    size="small"
                    @blur="handleInputBlur"
                    @keydown="handleInputKeydown"
                  />
                </div>
                <div
                  v-else
                  class="summary-text"
                  @click="startEditField(voucher.id, detailIndex, 'summary')"
                >
                  {{ detail.summary || '' }}
                </div>
              </td>

              <!-- 科目列 -->
              <td class="account-cell">
                <div
                  v-if="
                    editingField?.voucherId === voucher.id &&
                    editingField?.detailIndex === detailIndex &&
                    editingField?.field === 'account'
                  "
                  class="editing-field"
                >
                  <a-input
                    v-model:value="detail.account"
                    size="small"
                    @blur="handleInputBlur"
                    @keydown="handleInputKeydown"
                  />
                </div>
                <div
                  v-else
                  class="account-text"
                  @click="startEditField(voucher.id, detailIndex, 'account')"
                >
                  {{ detail.account || '' }}
                </div>
              </td>

              <!-- 借方金额列 -->
              <td class="debit-cell">
                <div
                  v-if="
                    editingField?.voucherId === voucher.id &&
                    editingField?.detailIndex === detailIndex &&
                    editingField?.field === 'debit'
                  "
                  class="editing-field"
                >
                  <a-input-number
                    v-model:value="detail.debit"
                    size="small"
                    :min="0"
                    :precision="2"
                    @blur="handleInputBlur"
                    @keydown="handleInputKeydown"
                  />
                </div>
                <div
                  v-else
                  class="amount-text debit"
                  @click="startEditField(voucher.id, detailIndex, 'debit')"
                >
                  <span v-if="detail.debit > 0">
                    ¥{{ formatNumber(detail.debit) }}
                  </span>
                  <span v-else class="placeholder-text"></span>
                </div>
              </td>

              <!-- 贷方金额列 -->
              <td class="credit-cell">
                <div
                  v-if="
                    editingField?.voucherId === voucher.id &&
                    editingField?.detailIndex === detailIndex &&
                    editingField?.field === 'credit'
                  "
                  class="editing-field"
                >
                  <a-input-number
                    v-model:value="detail.credit"
                    size="small"
                    :min="0"
                    :precision="2"
                    @blur="handleInputBlur"
                    @keydown="handleInputKeydown"
                  />
                </div>
                <div
                  v-else
                  class="amount-text credit"
                  @click="startEditField(voucher.id, detailIndex, 'credit')"
                >
                  <span v-if="detail.credit > 0">
                    ¥{{ formatNumber(detail.credit) }}
                  </span>
                  <span v-else class="placeholder-text"></span>
                </div>
              </td>

              <!-- 操作列 -->
              <td class="actions-cell">
                <div class="action-buttons">
                  <a-button
                    type="text"
                    size="small"
                    danger
                    class="delete-btn"
                    @click="handleDeleteDetail(voucher.id, detailIndex)"
                    title="删除明细"
                  >
                    <DeleteOutlined />
                  </a-button>
                </div>
              </td>
            </tr>

            <!-- 合计行 -->
            <tr class="total-row">
              <td class="total-merged-cell"></td>
              <td class="total-summary">
                <div class="total-text">合计</div>
              </td>
              <td class="total-account"></td>
              <td class="total-debit">
                <div class="total-amount debit">
                  ¥{{ formatNumber(getTotalDebit(voucher)) }}
                </div>
              </td>
              <td class="total-credit">
                <div class="total-amount credit">
                  ¥{{ formatNumber(getTotalCredit(voucher)) }}
                </div>
              </td>
              <td class="total-actions"></td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>

    <!-- 底部分页 -->
    <div class="bottom-footer">
      <!-- 记录信息 -->
      <div class="pagination-info">
        <span class="record-info">
          当前 第 {{ (currentPage - 1) * pageSize + 1 }} 到
          {{ Math.min(currentPage * pageSize, totalVouchers) }} 条 , 总共
          {{ totalVouchers }} 条 记录
        </span>
      </div>

      <!-- 分页控件 -->
      <div class="pagination-controls">
        <!-- 页面大小选择 -->
        <a-select
          v-model:value="pageSize"
          @change="handlePageSizeChange"
          class="page-size-select"
          size="small"
        >
          <a-select-option
            v-for="option in smartPageSizeOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-select-option>
        </a-select>

        <!-- 快速跳转按钮 -->
        <a-button
          @click="jumpToFirstPage"
          :disabled="currentPage === 1"
          size="small"
          title="第一页"
        >
          ⏮
        </a-button>

        <a-button
          @click="handlePageChange(currentPage - 1)"
          :disabled="currentPage === 1"
          size="small"
        >
          上一页
        </a-button>

        <!-- 页码按钮 -->
        <div class="page-numbers">
          <template v-for="(page, index) in visiblePageNumbers" :key="index">
            <span v-if="page === -1" class="page-ellipsis">...</span>
            <a-button
              v-else
              @click="handlePageChange(page)"
              :type="page === currentPage ? 'primary' : 'default'"
              size="small"
              class="page-number-btn"
            >
              {{ page }}
            </a-button>
          </template>
        </div>

        <a-button
          @click="handlePageChange(currentPage + 1)"
          :disabled="currentPage === totalPages"
          size="small"
        >
          下一页
        </a-button>

        <a-button
          @click="jumpToLastPage"
          :disabled="currentPage === totalPages"
          size="small"
          title="最后一页"
        >
          ⏭
        </a-button>

        <!-- 跳转到指定页 -->
        <div class="jump-to-page">
          <span>到第</span>
          <a-input-number
            v-model:value="jumpPage"
            :min="1"
            :max="totalPages"
            size="small"
            class="jump-input"
            @press-enter="handleJumpToPage"
          />
          <span>页</span>
          <a-button @click="handleJumpToPage" type="primary" size="small">
            确定
          </a-button>
        </div>
      </div>
    </div>

    <!-- 原始数据详情弹窗 -->
    <div
      v-if="detailModalVisible"
      class="custom-modal-overlay"
      @click="detailModalVisible = false"
    >
      <div class="custom-modal" @click.stop>
        <div class="custom-modal-header">
          <h3>原始数据详情</h3>
          <div class="header-actions">
            <button class="close-btn" @click="detailModalVisible = false">
              ×
            </button>
          </div>
        </div>
        <div class="custom-modal-body">
          <div v-if="detailLoading" style="padding: 40px; text-align: center">
            <div class="loading-spinner"></div>
            <div style="margin-top: 16px">加载详情中...</div>
          </div>
          <div
            v-else-if="!detailData"
            style="padding: 40px; text-align: center"
          >
            <div>暂无详细数据</div>
          </div>
          <SourceDataDetail
            v-else
            :detail-data="detailData"
            :loading="detailLoading"
          />
        </div>
      </div>
    </div>

    <!-- 文件预览弹窗 -->
    <a-modal
      v-model:open="filePreviewVisible"
      :title="previewTitle"
      width="80%"
      :footer="null"
      :centered="true"
      @cancel="closeFilePreview"
    >
      <div class="file-preview-container">
        <!-- PDF 预览 -->
        <iframe
          v-if="getFileType(previewUrl) === 'pdf'"
          :src="previewUrl"
          class="pdf-preview"
          frameborder="0"
        ></iframe>

        <!-- 图片预览 -->
        <img
          v-else-if="getFileType(previewUrl) === 'image'"
          :src="previewUrl"
          :alt="previewTitle"
          class="image-preview"
        />

        <!-- 其他文件类型 -->
        <div v-else class="other-file-preview">
          <div class="file-icon">📄</div>
          <div class="file-info">
            <p>{{ previewTitle }}</p>
            <p class="file-tip">此文件类型不支持在线预览，请下载后查看</p>
            <a-button
              type="primary"
              @click="downloadFile(previewUrl, previewTitle)"
            >
              <template #icon>
                <DownloadOutlined />
              </template>
              下载文件
            </a-button>
          </div>
        </div>

        <!-- 多文件导航 -->
        <div v-if="previewFiles.length > 1" class="file-navigation">
          <a-button
            :disabled="currentFileIndex === 0"
            @click="
              currentFileIndex--;
              previewFile(
                previewFiles[currentFileIndex].fullUrl,
                getFileName(previewFiles[currentFileIndex].url),
              );
            "
          >
            上一个
          </a-button>
          <span class="file-counter">
            {{ currentFileIndex + 1 }} / {{ previewFiles.length }}
          </span>
          <a-button
            :disabled="currentFileIndex === previewFiles.length - 1"
            @click="
              currentFileIndex++;
              previewFile(
                previewFiles[currentFileIndex].fullUrl,
                getFileName(previewFiles[currentFileIndex].url),
              );
            "
          >
            下一个
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 合并凭证确认弹窗 -->
    <a-modal
      v-model:open="mergeConfirmVisible"
      title="合并凭证确认"
      :confirm-loading="mergeLoading"
      @ok="confirmMergeVouchers"
      @cancel="cancelMergeVouchers"
    >
      <div class="merge-confirm-content">
        <p>您确定要合并以下 {{ selectedVouchers.length }} 个银行回单凭证吗？</p>
        <div class="selected-vouchers-list">
          <div
            v-for="voucher in selectedVouchers"
            :key="voucher.id"
            class="selected-voucher-item"
          >
            <span class="voucher-info">
              {{ voucher.type }} - {{ String(voucher.id).padStart(3, '0') }}
            </span>
            <span class="voucher-date">{{ voucher.record_date }}</span>
            <span class="voucher-amount">
              借方: ¥{{ formatNumber(getTotalDebit(voucher)) }} | 贷方: ¥{{
                formatNumber(getTotalCredit(voucher))
              }}
            </span>
          </div>
        </div>
        <p class="merge-warning">
          <strong>注意：</strong>
          合并后将生成一个新的凭证，原凭证将被删除，此操作不可撤销。
        </p>
      </div>
    </a-modal>
  </div>
</template>

<style scoped>
  @keyframes modal-slide-in {
    from {
      opacity: 0;
      transform: scale(0.9) translateY(-20px);
    }

    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .top-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .header-left,
    .header-right {
      justify-content: center;
    }

    .bottom-footer {
      flex-direction: column;
      gap: 8px;
      align-items: stretch;
    }

    .pagination-info {
      justify-content: center;
    }

    .pagination-controls {
      flex-wrap: wrap;
      gap: 4px;
      justify-content: center;
    }

    .page-numbers {
      gap: 2px;
    }

    .page-number-btn {
      min-width: 28px;
      height: 24px;
      padding: 0 4px;
      font-size: 12px;
    }

    .jump-to-page {
      padding-top: 8px;
      padding-left: 0;
      margin-top: 8px;
      margin-left: 0;
      border-top: 1px solid #e8e8e8;
      border-left: none;
    }

    .page-size-select {
      width: 140px;
    }

    .table-container {
      overflow-x: auto;
    }

    .voucher-table {
      min-width: 800px;
    }
  }

  .voucher-overview {
    min-height: 100vh;
    padding: 16px;
    background: #f5f5f5;
  }

  .voucher-overview.with-generated-voucher {
    display: flex;
    flex-direction: column;
  }

  .voucher-overview.with-generated-voucher .main-content-wrapper {
    display: flex;
    gap: 20px;
  }

  .voucher-overview.with-generated-voucher .voucher-list-container {
    flex: 1;
    min-width: 0; /* 防止flex子项溢出 */
  }

  .voucher-overview.with-generated-voucher .generated-voucher-container {
    flex-shrink: 0;
    width: 400px;
    max-height: calc(100vh - 120px);
    padding: 16px;
    overflow-y: auto;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
  }

  .ai-generated-notice {
    margin-bottom: 16px;

    .ai-generated-actions {
      display: flex;
      gap: 8px;
    }
  }

  /* 顶部操作栏 */
  .top-header {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px 8px 0 0;
  }

  .header-left {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .search-container {
    position: relative;
  }

  .search-input {
    width: 200px;
  }

  .search-icon {
    color: #999;
  }

  .more-query-btn {
    display: flex;
    gap: 4px;
    align-items: center;
  }

  .header-right {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  /* 表格容器 */
  .table-container {
    overflow-x: auto;
    background: white;
    border: 1px solid #e8e8e8;
    border-top: none;
  }

  .voucher-table {
    width: 100%;
    font-size: 14px;
    border-collapse: collapse;
  }

  .voucher-table thead {
    background: #fafafa;
  }

  .voucher-table th {
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 500;
    color: #666;
    text-align: left;
    text-transform: uppercase;
    border-bottom: 1px solid #e8e8e8;
  }

  .col-merged {
    width: 32%;
  }

  .col-summary {
    width: 20%;
  }

  .col-account {
    width: 16%;
  }

  .col-debit {
    width: 12%;
    text-align: right;
  }

  .col-credit {
    width: 12%;
    text-align: right;
  }

  .col-actions {
    width: 8%;
    text-align: center;
  }

  /* 合并表头样式 */
  .merged-header {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .header-checkbox {
    flex-shrink: 0;
  }

  .header-divider {
    font-size: 12px;
    color: #d9d9d9;
  }

  .header-source {
    flex: 1;
  }

  /* 凭证头部行 */
  .voucher-header-row {
    background: #e6f7ff;
  }

  .voucher-header-cell {
    padding: 8px 16px;
    border-bottom: 1px solid #e8e8e8;
  }

  .voucher-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .voucher-info {
    display: flex;
    gap: 12px;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
  }

  .voucher-checkbox {
    margin-right: 8px;
  }

  .voucher-date {
    font-size: 12px;
    color: #666;
  }

  .voucher-number {
    font-size: 12px;
    color: #666;
  }

  .ai-badge {
    padding: 2px 8px;
    font-size: 12px;
    font-weight: 500;
    color: #856404;
    background: #fff3cd;
    border-radius: 4px;
  }

  .voucher-actions {
    display: flex;
    gap: 4px;
    align-items: center;
  }

  .action-btn {
    display: flex;
    gap: 4px;
    align-items: center;
    height: auto;
    padding: 4px 8px;
    font-size: 12px;
  }

  /* 明细行 */
  .detail-row {
    border-bottom: 1px solid #f0f0f0;
  }

  .detail-row:hover {
    background: #fafafa;
  }

  .voucher-table td {
    padding: 4px 16px;
    vertical-align: top;
  }

  /* 原始信息列 */
  .source-info-cell {
    border-right: 1px solid #e8e8e8;
  }

  .source-info-cell.merged-cell {
    position: relative;
    width: 32%;
  }

  .source-info {
    font-size: 11px;
  }

  .source-type {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
    font-weight: 600;
    color: #1890ff;
  }

  .source-title-section {
    display: flex;
    gap: 6px;
    align-items: center;
  }

  .source-title {
    font-size: 12px;
    font-weight: 600;
  }

  .source-invoice-number {
    font-size: 11px;
    font-weight: 400;
    color: #666;
  }

  .source-actions {
    display: flex;
    gap: 3px;
    align-items: center;
  }

  .original-files-link,
  .details-link {
    height: auto;
    padding: 0;
    font-size: 11px;
  }

  .original-files-link {
    color: #1890ff;
  }

  .original-files-link:hover {
    color: #40a9ff;
  }

  /* 原始文件内联显示样式 */
  .original-files-section {
    padding-top: 6px;
    margin-top: 8px;
    border-top: 1px solid #e8e8e8;
  }

  .files-header {
    margin-bottom: 4px;
  }

  .files-title {
    display: flex;
    gap: 3px;
    align-items: center;
    font-size: 11px;
    font-weight: 500;
    color: #666;
  }

  .files-title::before {
    font-size: 12px;
    content: '📎';
  }

  .files-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3px;
  }

  /* 当只有一个文件时，让它跨两列 */
  .files-list .file-item-inline:only-child {
    grid-column: 1 / -1;
  }

  .file-item-inline {
    display: flex;
    gap: 4px;
    align-items: center;
    min-width: 0;
    padding: 2px 4px;
    cursor: pointer;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .file-item-inline:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
    transform: translateY(-1px);
  }

  .file-icon-small {
    flex-shrink: 0;
    width: 14px;
    font-size: 11px;
    text-align: center;
  }

  .file-name-small {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    font-size: 10px;
    line-height: 1.2;
    color: #333;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .source-content {
    color: #666;
  }

  .source-item {
    margin-bottom: 2px;
    line-height: 1.2;
  }

  /* 一行两个信息项的布局 */
  .source-item-row {
    display: flex;
    gap: 8px;
    margin-bottom: 2px;
  }

  .source-item-half {
    flex: 1;
    min-width: 0;
    font-size: 11px;
    line-height: 1.2;
  }

  .merged-badge {
    display: inline-block;
    padding: 1px 4px;
    margin-left: 4px;
    font-size: 10px;
    color: #1890ff;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 2px;
  }

  /* 摘要和科目列 */
  .summary-cell,
  .account-cell {
    font-size: 12px;
  }

  .summary-text,
  .account-text {
    line-height: 1.4;
    color: #333;
  }

  /* 金额列 */
  .debit-cell,
  .credit-cell {
    text-align: right;
    vertical-align: middle;
  }

  .debit-cell .placeholder-text,
  .credit-cell .placeholder-text,
  .debit-cell .amount-text,
  .credit-cell .amount-text {
    justify-content: flex-end;
    width: 100%;
    text-align: right;
  }

  .amount-text {
    font-size: 12px;
    font-weight: 600;
  }

  .amount-text.debit {
    color: #52c41a;
  }

  .amount-text.credit {
    color: #f5222d;
  }

  /* 操作列 */
  .actions-cell {
    text-align: center;
  }

  .action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
  }

  .delete-btn,
  .edit-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    padding: 4px;
  }

  /* 合计行 */
  .total-row {
    font-weight: 600;
    background: #f0f0f0;
    border-top: 1px solid #d9d9d9;
  }

  .total-row td {
    background: #f0f0f0 !important;
  }

  .total-merged-cell,
  .total-summary,
  .total-account,
  .total-debit,
  .total-credit,
  .total-actions {
    background: #f0f0f0;
  }

  .total-summary {
    font-size: 12px;
  }

  .total-text {
    color: #333;
  }

  .total-debit,
  .total-credit {
    text-align: right;
  }

  .total-amount {
    font-size: 12px;
    font-weight: 600;
  }

  .total-amount.debit {
    color: #389e0d;
  }

  .total-amount.credit {
    color: #cf1322;
  }

  /* 底部分页 */
  .bottom-footer {
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    font-size: 14px;
    color: #666;
    background: white;
    border: 1px solid #e8e8e8;
    border-top: none;
    border-radius: 0 0 8px 8px;
  }

  .pagination-info {
    display: flex;
    flex-shrink: 0;
    align-items: center;
  }

  .record-info {
    font-size: 13px;
    color: #666;
  }

  .smart-size-btn {
    height: 24px;
    padding: 0 8px;
    font-size: 12px;
  }

  .pagination-controls {
    display: flex;
    flex: 1;
    flex-wrap: nowrap;
    gap: 8px;
    align-items: center;
    justify-content: flex-end;
  }

  .page-size-select {
    flex-shrink: 0;
    width: 120px;
  }

  .page-numbers {
    display: flex;
    gap: 4px;
    align-items: center;
  }

  .page-number-btn {
    min-width: 32px;
    height: 28px;
    padding: 0 8px;
  }

  .page-ellipsis {
    padding: 0 4px;
    font-size: 12px;
    color: #999;
  }

  .jump-to-page {
    display: flex;
    flex-shrink: 0;
    gap: 4px;
    align-items: center;
    padding-left: 12px;
    margin-left: 12px;
    border-left: 1px solid #e8e8e8;
  }

  .jump-input {
    width: 50px;
  }

  /* 自定义弹窗样式 */
  .custom-modal-overlay {
    position: fixed;
    inset: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgb(0 0 0 / 60%);
    backdrop-filter: blur(4px);
  }

  .custom-modal {
    width: 90vw;
    max-width: 1000px;
    max-height: 80vh;
    overflow: hidden;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgb(0 0 0 / 30%);
    animation: modal-slide-in 0.3s ease-out;
  }

  .custom-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  .custom-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    padding: 0;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    background: none;
    border: none;
    border-radius: 6px;
    transition: all 0.2s;
  }

  .close-btn:hover {
    color: #333;
    background: #f5f5f5;
  }

  .custom-modal-body {
    max-height: calc(80vh - 80px);
    padding: 24px;
    overflow-y: auto;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  /* AI生成凭证样式 */
  .voucher-header-row.ai-generated {
    background-color: #f0f7ff;

    &:hover {
      background-color: #e6f4ff;
    }
  }

  .detail-row.ai-generated {
    background-color: #f8fbff;

    &:hover {
      background-color: #f0f7ff;
    }
  }

  /* 编辑相关样式 */
  .editing-field {
    width: 100%;
  }

  .placeholder-text {
    display: inline-block;
    min-width: 60px;
    min-height: 20px;
    padding: 8px 4px;
    font-style: italic;
    color: #bbb;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  /* 金额列中的占位符文本需要特殊处理 */
  .debit-cell .placeholder-text,
  .credit-cell .placeholder-text {
    display: block;
    width: calc(100% - 8px);
    margin: 0 auto;
  }

  .placeholder-text:hover {
    background-color: #f5f5f5;
  }

  .summary-text,
  .account-text,
  .amount-text {
    display: inline-block;
    min-width: 60px;
    min-height: 20px;
    padding: 8px 4px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .summary-text:hover,
  .account-text:hover,
  .amount-text:hover {
    background-color: #f5f5f5;
  }

  .summary-text:empty::before,
  .account-text:empty::before {
    font-style: italic;
    color: #bbb;
  }

  /* 合并相关样式 */
  .voucher-checkbox-cell {
    text-align: center;
    vertical-align: middle;
    border-right: 1px solid #e8e8e8;
  }

  .total-merged-cell {
    width: 32%;
    border-right: 1px solid #e8e8e8;
  }

  .merge-confirm-content {
    padding: 16px 0;
  }

  .selected-vouchers-list {
    max-height: 300px;
    margin: 16px 0;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
  }

  .selected-voucher-item {
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-bottom: 1px solid #f5f5f5;
  }

  .selected-voucher-item:last-child {
    border-bottom: none;
  }

  .selected-voucher-item .voucher-info {
    font-weight: 500;
    color: #333;
  }

  .selected-voucher-item .voucher-date {
    font-size: 12px;
    color: #666;
  }

  .selected-voucher-item .voucher-amount {
    font-size: 12px;
    color: #999;
  }

  .merge-warning {
    padding: 12px;
    font-size: 13px;
    color: #fa8c16;
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 4px;
  }

  /* 文件预览样式 */
  .file-preview-container {
    position: relative;
    min-height: 400px;
  }

  .pdf-preview {
    width: 100%;
    height: 600px;
    border: none;
  }

  .image-preview {
    display: block;
    max-width: 100%;
    max-height: 600px;
    margin: 0 auto;
    object-fit: contain;
  }

  .other-file-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    text-align: center;
  }

  .other-file-preview .file-icon {
    margin-bottom: 16px;
    font-size: 48px;
  }

  .file-info p {
    margin: 8px 0;
  }

  .file-tip {
    font-size: 14px;
    color: #666;
  }

  .file-navigation {
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: center;
    padding: 12px;
    margin-top: 16px;
    background: #f5f5f5;
    border-radius: 6px;
  }

  .file-counter {
    min-width: 80px;
    font-size: 14px;
    color: #666;
    text-align: center;
  }
</style>
