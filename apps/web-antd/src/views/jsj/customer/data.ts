import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'customerName',
    label: '客户名称',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.COMPUTE_TYPE 便于维护
      options: getDictOptions('compute_type'),
    },
    fieldName: 'computeType',
    label: '计税方式',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.TAX_TYPE 便于维护
      options: getDictOptions('tax_type'),
    },
    fieldName: 'taxType',
    label: '纳税人类型',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.INCOME_TYPE 便于维护
      options: getDictOptions('income_type'),
    },
    fieldName: 'incomeType',
    label: '主要收入',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '客户ID',
    field: 'customerId',
  },
  {
    title: '客户名称',
    field: 'customerName',
  },
  {
    title: '计税方式',
    field: 'computeType',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.COMPUTE_TYPE 便于维护
        return renderDict(row.computeType, 'compute_type');
      },
    },
  },
  {
    title: '纳税人类型',
    field: 'taxType',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.TAX_TYPE 便于维护
        return renderDict(row.taxType, 'tax_type');
      },
    },
  },
  {
    title: '主要收入',
    field: 'incomeType',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.INCOME_TYPE 便于维护
        return renderDict(row.incomeType, 'income_type');
      },
    },
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '客户ID',
    fieldName: 'customerId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '客户名称',
    fieldName: 'customerName',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '计税方式',
    fieldName: 'computeType',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.COMPUTE_TYPE 便于维护
      options: getDictOptions('compute_type'),
    },
    rules: 'selectRequired',
  },
  {
    label: '纳税人类型',
    fieldName: 'taxType',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.TAX_TYPE 便于维护
      options: getDictOptions('tax_type'),
    },
    rules: 'selectRequired',
  },
  {
    label: '主要收入',
    fieldName: 'incomeType',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.INCOME_TYPE 便于维护
      options: getDictOptions('income_type'),
    },
    rules: 'selectRequired',
  },
  {
    label: '备注',
    fieldName: 'remark',
    component: 'Input',
  },
];
