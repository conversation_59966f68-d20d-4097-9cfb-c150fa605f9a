# API路径验证文档

## 问题描述

用户指出完整的API请求路径应该是：
```
/prod-api/autojob/api/invoice/list?company_name=青岛鼎信诚装饰工程有限公司&input_output=output
```

## 当前配置分析

### 环境变量配置
根据项目配置文件：

**开发环境** (`.env.development`):
```bash
VITE_GLOB_API_URL=/prod-api
```

**生产环境** (`.env.production`):
```bash
VITE_GLOB_API_URL=/prod-api
```

### API客户端配置

#### 1. 全局requestClient配置
位置：`apps/web-antd/src/api/request.ts`
```typescript
export const requestClient = createRequestClient(apiURL);
// apiURL = /prod-api (来自环境变量)
```

#### 2. 原始发票专用客户端配置
位置：`apps/web-antd/src/api/original-voucher/index.ts`
```typescript
const requestClient = new RequestClient({
  baseURL: '/prod-api/autojob/api/',  // 完整路径
  timeout: 10000,
});
```

#### 3. 新版本API客户端配置 (已修复)
位置：`apps/web-antd/src/api/original-voucher/api-v2.ts`
```typescript
const requestClient = new RequestClient({
  baseURL: '/prod-api/autojob/api',   // 完整路径
  timeout: 10000,
});
```

## 路径组合分析

### 修复前的问题
```
全局requestClient baseURL: /prod-api
API端点: /invoice/list
最终路径: /prod-api/invoice/list ❌ (缺少 /autojob/api)
```

### 修复后的正确路径
```
专用requestClient baseURL: /prod-api/autojob/api
API端点: /invoice/list
最终路径: /prod-api/autojob/api/invoice/list ✅
```

## 完整请求URL示例

### 发票列表请求
```
GET /prod-api/autojob/api/invoice/list?company_name=青岛鼎信诚装饰工程有限公司&input_output=output
```

### 银行回单列表请求
```
GET /prod-api/autojob/api/bank_receipts/list?company_name=青岛鼎信诚装饰工程有限公司
```

## 修复内容

### 1. 更新api-v2.ts的requestClient配置
```typescript
// 修复前
import { requestClient } from '#/api/request';

// 修复后
import { RequestClient } from '@vben/request';

const requestClient = new RequestClient({
  baseURL: '/prod-api/autojob/api',
  timeout: 10000,
});
```

### 2. 确保API端点路径正确
```typescript
// 发票列表
export function getInvoiceListV2(params: InvoiceQueryParams) {
  return requestClient.get<FullApiResponse<InvoiceData>>('/invoice/list', { 
    params,
    responseReturn: 'raw'
  });
}

// 银行回单列表
export function getBankReceiptListV2(params: BankReceiptQueryParams) {
  return requestClient.get<FullApiResponse<BankReceiptData>>('/bank_receipts/list', { 
    params,
    responseReturn: 'raw'
  });
}
```

## 验证方法

### 1. 浏览器开发者工具验证
1. 打开原始发票页面
2. 打开浏览器开发者工具的Network标签
3. 选择公司并触发数据查询
4. 检查请求URL是否为：`/prod-api/autojob/api/invoice/list`

### 2. 控制台日志验证
在代码中已添加调试日志：
```typescript
console.log('发票查询参数:', params);
console.log('发票原始响应:', result.originalResponse);
```

### 3. 预期的网络请求
```
Request URL: /prod-api/autojob/api/invoice/list
Query Parameters:
  company_name: 青岛鼎信诚装饰工程有限公司
  input_output: output
```

## 其他相关API的路径配置

项目中其他API也使用了相同的路径模式：

### 公司列表API
```typescript
// apps/web-antd/src/api/companies/index.ts
return requestClient.get<CompaniesResponse>(
  '/prod-api/autojob/api/companies/list',
  { params }
);
```

### 凭证API
```typescript
// apps/web-antd/src/api/tool/voucher.ts
return client.get<VoucherResponse>('/prod-api/autojob/api/vouchers/current', {
  params: { company_name, month }
});
```

### 用户信息API
```typescript
// apps/web-antd/src/api/tool/company.ts
return client.get<UserCustomerNamesResponse>(
  '/prod-api/autojob/api/users/get_user_info',
  { params: { username, tenant_id } }
);
```

## 总结

✅ **已修复**：api-v2.ts中的requestClient配置，使用正确的baseURL
✅ **已确认**：API端点路径配置正确
✅ **已验证**：与项目中其他API的路径模式保持一致

现在API请求应该能够正确访问到：
`/prod-api/autojob/api/invoice/list?company_name=xxx&input_output=xxx`
