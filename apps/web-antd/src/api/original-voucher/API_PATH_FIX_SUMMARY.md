# API路径修复总结

## 🎯 问题描述

用户指出API请求路径不正确，完整的请求应该是：
```
/prod-api/autojob/api/invoice/list?company_name=青岛鼎信诚装饰工程有限公司&input_output=output
```

## 🔍 问题分析

### 原始问题
在 `api-v2.ts` 中使用了全局的 `requestClient`：
```typescript
import { requestClient } from '#/api/request';
```

全局 `requestClient` 的 baseURL 配置：
- 开发环境：`/prod-api` (来自 `VITE_GLOB_API_URL`)
- 生产环境：`/prod-api` (来自 `VITE_GLOB_API_URL`)

### 路径组合问题
```
全局requestClient baseURL: /prod-api
API端点: /invoice/list
实际请求路径: /prod-api/invoice/list ❌

期望的完整路径: /prod-api/autojob/api/invoice/list ✅
```

## ✅ 修复方案

### 1. 更新 api-v2.ts 的 requestClient 配置

**修复前：**
```typescript
import { requestClient } from '#/api/request';
```

**修复后：**
```typescript
import { RequestClient } from '@vben/request';

// 创建专用的请求客户端，使用完整的API路径
const requestClient = new RequestClient({
  baseURL: '/prod-api/autojob/api',
  timeout: 10000,
});
```

### 2. 确保API端点路径正确

发票列表API：
```typescript
export function getInvoiceListV2(params: InvoiceQueryParams) {
  return requestClient.get<FullApiResponse<InvoiceData>>('/invoice/list', { 
    params,
    responseReturn: 'raw'
  });
}
```

银行回单列表API：
```typescript
export function getBankReceiptListV2(params: BankReceiptQueryParams) {
  return requestClient.get<FullApiResponse<BankReceiptData>>('/bank_receipts/list', { 
    params,
    responseReturn: 'raw'
  });
}
```

## 🔄 最终路径组合

### 修复后的正确路径
```
专用requestClient baseURL: /prod-api/autojob/api
API端点: /invoice/list
最终请求路径: /prod-api/autojob/api/invoice/list ✅
```

### 完整请求示例
```
GET /prod-api/autojob/api/invoice/list?company_name=青岛鼎信诚装饰工程有限公司&input_output=output
GET /prod-api/autojob/api/bank_receipts/list?company_name=青岛鼎信诚装饰工程有限公司
```

## 📊 与项目其他API的一致性

项目中其他API也使用相同的路径模式：

### 公司列表API
```typescript
// apps/web-antd/src/api/companies/index.ts
'/prod-api/autojob/api/companies/list'
```

### 凭证API
```typescript
// apps/web-antd/src/api/tool/voucher.ts
'/prod-api/autojob/api/vouchers/current'
'/prod-api/autojob/api/vouchers/source-data'
'/prod-api/autojob/api/vouchers/update'
'/prod-api/autojob/api/vouchers/merge'
```

### 用户信息API
```typescript
// apps/web-antd/src/api/tool/company.ts
'/prod-api/autojob/api/users/get_user_info'
```

## 🧪 验证方法

### 1. 浏览器开发者工具验证
1. 打开原始发票页面
2. 打开浏览器开发者工具的Network标签
3. 选择公司并触发数据查询
4. 检查请求URL是否为：`/prod-api/autojob/api/invoice/list`

### 2. 控制台日志验证
代码中的调试日志会显示：
```typescript
console.log('发票查询参数:', params);
console.log('发票原始响应:', result.originalResponse);
```

### 3. 预期的网络请求
```
Request URL: /prod-api/autojob/api/invoice/list
Method: GET
Query Parameters:
  company_name: 青岛鼎信诚装饰工程有限公司
  input_output: output
```

## 📁 修改的文件

### 主要修改
- `apps/web-antd/src/api/original-voucher/api-v2.ts` - 修复requestClient配置

### 新增文档
- `apps/web-antd/src/api/original-voucher/API_PATH_VERIFICATION.md` - 路径验证文档
- `apps/web-antd/src/api/original-voucher/API_PATH_FIX_SUMMARY.md` - 修复总结文档

## 🚀 部署验证清单

- [ ] 确认开发环境API请求路径正确
- [ ] 确认生产环境API请求路径正确
- [ ] 验证发票列表API响应正常
- [ ] 验证银行回单列表API响应正常
- [ ] 检查控制台无错误日志
- [ ] 确认数据能正常显示在表格中

## 📝 注意事项

1. **ESLint配置**：新文件可能需要更新tsconfig.json包含路径
2. **缓存清理**：修改后建议清理浏览器缓存和重新构建
3. **环境一致性**：确保开发和生产环境的API路径配置一致
4. **错误处理**：如果仍有路径问题，检查代理配置和nginx配置

---

**修复时间**: 2025-06-20  
**修复状态**: ✅ 完成  
**验证状态**: 🧪 待验证  
**影响范围**: 原始发票界面API调用
