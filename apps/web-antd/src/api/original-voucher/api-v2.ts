import { RequestClient } from '@vben/request';
import type {
  BankReceiptData,
  BankReceiptQueryParams,
  BusinessApiResponse,
  FullApiResponse,
  InvoiceData,
  InvoiceQueryParams,
  PayrollData,
  PayrollQueryParams,
} from './types';

// 创建专用的请求客户端，使用完整的API路径
const requestClient = new RequestClient({
  baseURL: '/prod-api/autojob/api',
  timeout: 10000,
});

/**
 * 查询发票列表 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 发票列表
 */
export function getInvoiceListV2(params: InvoiceQueryParams) {
  return requestClient.get<FullApiResponse<InvoiceData>>('/invoice/list', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 查询银行回单列表 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 银行回单列表
 */
export function getBankReceiptListV2(params: BankReceiptQueryParams) {
  return requestClient.get<FullApiResponse<BankReceiptData>>('/bank_receipts/list', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 查询工资单列表 (V2 - 处理实际的嵌套响应结构)
 * @param params 查询参数
 * @returns 工资单列表
 */
export function getPayrollListV2(params: PayrollQueryParams) {
  return requestClient.get<FullApiResponse<PayrollData>>('/payroll/list', {
    params,
    responseReturn: 'raw' // 返回完整的axios响应
  });
}

/**
 * 处理API响应，提取业务数据
 * @param response 完整的API响应
 * @returns 业务数据
 */
export function extractBusinessData<T>(response: FullApiResponse<T>): BusinessApiResponse<T> {
  return response.data;
}

/**
 * 检查业务响应是否成功
 * @param businessResponse 业务响应
 * @returns 是否成功
 */
export function isBusinessSuccess<T>(businessResponse: BusinessApiResponse<T>): boolean {
  return businessResponse.status === 'success';
}

/**
 * 获取业务数据数组
 * @param businessResponse 业务响应
 * @returns 数据数组
 */
export function getBusinessDataArray<T>(businessResponse: BusinessApiResponse<T>): T[] {
  return businessResponse.data || [];
}

/**
 * 统一的API调用处理函数
 * @param apiCall API调用函数
 * @returns 处理后的业务数据
 */
export async function handleApiCall<T>(
  apiCall: () => Promise<FullApiResponse<T>>
): Promise<{
  success: boolean;
  data: T[];
  message: string;
  originalResponse?: FullApiResponse<T>;
}> {
  try {
    const response = await apiCall();
    const businessData = extractBusinessData(response);
    
    return {
      success: isBusinessSuccess(businessData),
      data: getBusinessDataArray(businessData),
      message: businessData.message || '操作成功',
      originalResponse: response,
    };
  } catch (error: any) {
    console.error('API调用失败:', error);
    return {
      success: false,
      data: [],
      message: error?.message || '请求失败',
    };
  }
}

/**
 * 查询发票列表的便捷方法
 * @param params 查询参数
 * @returns 处理后的发票数据
 */
export async function fetchInvoiceList(params: InvoiceQueryParams) {
  return handleApiCall(() => getInvoiceListV2(params));
}

/**
 * 查询银行回单列表的便捷方法
 * @param params 查询参数
 * @returns 处理后的银行回单数据
 */
export async function fetchBankReceiptList(params: BankReceiptQueryParams) {
  return handleApiCall(() => getBankReceiptListV2(params));
}

/**
 * 查询工资单列表的便捷方法
 * @param params 查询参数
 * @returns 处理后的工资单数据
 */
export async function fetchPayrollList(params: PayrollQueryParams) {
  return handleApiCall(() => getPayrollListV2(params));
}
