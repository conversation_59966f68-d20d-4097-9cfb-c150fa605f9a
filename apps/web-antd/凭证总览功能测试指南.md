# 凭证总览功能测试指南

## 功能完成情况

✅ **已完成的功能**

### 1. 菜单配置
- ✅ 在"概览"菜单下添加了"凭证总览"菜单项
- ✅ 配置了正确的路由路径 `/voucher-overview`
- ✅ 添加了文件图标 `lucide:file-text`
- ✅ 配置了中英文国际化支持

### 2. 页面结构
- ✅ 创建了主页面组件 `apps/web-antd/src/views/dashboard/voucher-overview/index.vue`
- ✅ 实现了筛选器功能（按类型和审核状态筛选）
- ✅ 实现了卡牌式凭证展示
- ✅ 支持实时数据更新

### 3. 凭证卡片组件
- ✅ 创建了 `VoucherCard.vue` 组件
- ✅ 显示原始数据信息和凭证信息
- ✅ 支持在线编辑凭证字段
- ✅ 实现了借贷平衡验证
- ✅ 支持添加/删除凭证明细
- ✅ 实现了审核状态切换
- ✅ 提供保存和删除功能

### 4. 原始数据详情
- ✅ 创建了 `SourceDataDetail.vue` 组件
- ✅ 支持弹窗显示详细信息
- ✅ 针对不同类型数据提供专门的展示格式：
  - 进项发票/销项发票信息
  - 银行回单信息（含收支统计）
  - 工资单信息（含成本分析）
- ✅ 显示凭证明细和借贷平衡状态

### 5. WebSocket 通信
- ✅ 创建了 `useWebSocket.ts` 组合式函数
- ✅ 支持自动重连机制
- ✅ 实现了完整的通信协议：
  - 获取凭证列表
  - 更新凭证
  - 删除凭证
  - 获取原始数据详情
  - 实时推送新凭证

### 6. 类型定义
- ✅ 创建了完整的 TypeScript 类型定义
- ✅ 支持所有数据结构的类型安全

### 7. Mock 服务器
- ✅ 创建了完整的 WebSocket Mock 服务器
- ✅ 提供了 6 条测试数据，涵盖所有凭证类型
- ✅ 支持所有 CRUD 操作
- ✅ 模拟实时推送功能
- ✅ 提供了启动脚本和文档

## 测试步骤

### 1. 启动服务

1. **启动 Mock 服务器**
```bash
cd apps/web-antd/src/mock
npm install
npm start
```
服务器将在 `ws://localhost:8080` 启动

2. **启动前端应用**
```bash
cd apps/web-antd
npm run dev
```
应用将在 `http://localhost:5667` 启动

### 2. 访问功能

1. 登录系统
2. 在左侧菜单中找到"概览"菜单
3. 点击展开后应该能看到"凭证总览"菜单项
4. 点击进入凭证总览页面

### 3. 功能测试

#### 3.1 基本展示测试
- ✅ 页面应显示 6 张凭证卡片
- ✅ 每张卡片包含原始数据和凭证信息两部分
- ✅ 卡片头部显示类型标签和审核状态

#### 3.2 筛选功能测试
- ✅ 测试按类型筛选：进项发票、销项发票、银行回单、工资单
- ✅ 测试按审核状态筛选：已审核、未审核
- ✅ 测试多选组合筛选

#### 3.3 凭证编辑测试
- ✅ 点击凭证字段进行编辑
- ✅ 修改凭证类型、日期等基本信息
- ✅ 添加新的凭证明细行
- ✅ 删除现有凭证明细
- ✅ 修改借方/贷方金额
- ✅ 验证借贷平衡检查
- ✅ 点击保存按钮提交更改

#### 3.4 原始数据详情测试
- ✅ 点击"详情"按钮打开弹窗
- ✅ 查看不同类型原始数据的详细信息
- ✅ 验证数据分析和统计功能

#### 3.5 审核功能测试
- ✅ 点击审核复选框切换审核状态
- ✅ 验证审核状态自动保存
- ✅ 查看已审核凭证的绿色边框

#### 3.6 实时通信测试
- ✅ 验证页面加载时自动获取数据
- ✅ 测试凭证更新的实时同步
- ✅ 测试凭证删除功能
- ✅ 观察 Mock 服务器的自动推送功能（每30秒10%概率）

## 预期结果

### 页面展示
- 页面顶部有筛选器区域，包含6个复选框
- 主体区域以卡牌形式展示凭证
- 每张卡片结构清晰，信息完整

### 交互功能
- 所有编辑操作响应迅速
- 数据验证正确（借贷平衡等）
- 筛选功能工作正常
- 弹窗详情显示完整

### 实时通信
- WebSocket 连接正常
- 数据同步及时
- 错误处理得当

## 故障排除

### 常见问题

1. **菜单项不显示**
   - 检查路由配置是否正确
   - 确认国际化文件已更新
   - 重启前端应用

2. **WebSocket 连接失败**
   - 确认 Mock 服务器已启动
   - 检查端口 8080 是否被占用
   - 查看浏览器控制台错误

3. **数据不显示**
   - 检查 WebSocket 连接状态
   - 查看网络请求是否正常
   - 确认 Mock 服务器返回数据

4. **编辑功能异常**
   - 检查数据格式是否正确
   - 验证借贷平衡逻辑
   - 查看组件状态更新

### 调试方法

1. **浏览器开发者工具**
   - Network 标签查看 WebSocket 连接
   - Console 标签查看错误信息
   - Vue DevTools 查看组件状态

2. **服务器日志**
   - 查看 Mock 服务器控制台输出
   - 检查消息收发情况

## 技术架构

### 前端技术栈
- Vue 3 + TypeScript
- Ant Design Vue
- Vite 构建工具
- WebSocket 实时通信

### 组件架构
```
voucher-overview/
├── index.vue                    # 主页面
├── types.ts                     # 类型定义
├── composables/
│   └── useWebSocket.ts         # WebSocket 逻辑
└── components/
    ├── VoucherCard.vue         # 凭证卡片
    └── SourceDataDetail.vue    # 详情弹窗
```

### 数据流
```
WebSocket Server ←→ useWebSocket ←→ 主页面 ←→ 子组件
```

## 扩展建议

### 短期优化
1. 添加加载状态指示器
2. 优化错误提示信息
3. 增加操作确认对话框
4. 添加键盘快捷键支持

### 长期扩展
1. 批量操作功能
2. 高级搜索和筛选
3. 数据导出功能
4. 权限管理集成
5. 审计日志记录

这个凭证总览功能已经完整实现了所有要求的功能，提供了完善的用户体验和技术架构。
